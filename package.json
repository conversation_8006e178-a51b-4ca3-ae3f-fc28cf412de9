{"name": "order-domain", "description": "", "private": true, "repository": {"type": "git", "url": "***********************:retail-web/order-domain.git"}, "scripts": {"linkAll": "lerna run link --parallel", "unlinkAll": "lerna run unlink --parallel", "bootstrap": "lerna bootstrap", "clean": "rm -rf packages/{utils,definitions,pc-components}/es", "prepublishOnly": "yarn build", "build": "yarn bootstrap && yarn clean && lerna run build --stream", "mkdir:es": "mkdir packages/{utils,definitions,pc-components}/es", "yalc:watch": "yarn bootstrap && yarn clean && yarn mkdir:es && lerna run yalc:watch --parallel", "yalc:link": "lerna run yalc:link --parallel", "pub": "lerna run build && lerna publish --force-publish", "pub:beta": "lerna run build && lerna publish --force-publish --dist-tag beta"}, "devDependencies": {"@babel/preset-react": "^7.13.13", "@youzan/eslint-config-retail": "^1.13.1", "@youzan/zan-doc": "^1.1.0-alpha.1", "concurrently": "^7.0.0", "eslint": "^7.29.0", "eslint-plugin-prettier": "^3.4.0", "husky": "^6.0.0", "lerna": "3.22.1", "lerna-changelog": "^2.1.0", "lint-staged": "^11.0.0", "nodemon": "^2.0.15", "prettier": "^2.3.2", "typescript": "^4.2.4", "yalc": "^1.0.0-pre.53"}, "workspaces": ["packages/*"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*.{ts,tsx}": "eslint"}}
# order-domain


## 本地调试
为了解决每次修改代码之后，验证在业务仓库里是否运行正常，每次都需要发包的问题，可以使用「本地调试」：
全局安装 `yalc`
`yarn global add yalc`

order-domain
如果只开发其中的某个模块时，以 pc-components 举例：
`cd packages/pc-components && yarn build:watch`

如果开发`pc-components`、`definitions`：
`yarn yalc:watch`
`yarn yalc:link` 
// 注意！！！需要待 `yarn yalc:watch` 命令没有输出后， 再执行这条命令

业务仓库
如果只开发其中的某个模块时，以 `pc-components` 举例：
`cd client && yalc add "@youzan/order-domain-pc-components" && yarn`

如果开发`pc-components`、`definitions`：
`cd client && yalc add "@youzan/order-domain-pc-components" && yalc add "@youzan/order-domain-definitions" && yarn`

上面的命令会改变业务仓库下 package.json 的内容。
由于 yalc add 之后，会在对应的目录下写入文件，所以需要在.gitignore 文件追加以下内容:
`.yalc`
`yalc.lock`

最后，只需要修改相应代码，观察以下操作确定本地调试生效：
1. order-domain 命令行是否有输出；
2. 业务仓库是否 rebuild；
3. 页面是否发生变化、逻辑是否发生变更。

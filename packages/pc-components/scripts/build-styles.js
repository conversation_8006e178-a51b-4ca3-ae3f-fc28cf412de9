/* eslint-disable @youzan/yz-retail/prefer-pascal-case-const, no-restricted-syntax */
// @ts-check
const fs = require('fs-extra');
const glob = require('globby');
const sass = require('sass');
const path = require('path');
const postcss = require('postcss').default;

const cwd = process.cwd();
const outDir = 'css'; // 基于 cwd 的相对路径
const zanDocStyleFile = 'doc.scss'; // 基于 cwd 的相对路径
const postcssConfig = require('../postcss.config');

const createOutputPath = (p) => path.resolve(cwd, outDir, p);
const readFileFromRoot = (p) => path.resolve(cwd, p);
const writeOutFile = (file, content) => fs.outputFile(createOutputPath(file), content);
const createOutputFile = (p) => p.replace('src/', '').replace(path.extname(p), '.css');
const docSassDefault = `// 自动化写入，请勿手写修改，执行 yarn build:scss 得到最新结果\n@import '~@youzan/order-domain-pc-components/css/index.css';`;

async function run() {
  await fs.emptyDir(createOutputPath('.'));

  let totalCss = ``;
  /** @type string[] */
  const docSassEntries = [];
  for await (const oFilepath of glob.stream(['src/**/*.s{a,c}ss'], {
    gitignore: true,
    cwd
  })) {
    const filepath = oFilepath.toString();
    // only push index file
    if (/index\.[^\.]+$/i.test(filepath)) {
      docSassEntries.push(filepath);
    }
    const sassResult = sass.renderSync({
      file: readFileFromRoot(filepath),
      importer: [(url) => ({ file: require.resolve(url) })]
    });
    const cssResult = sassResult.css.toString();
    // @ts-ignore
    const postcssResult = await postcss(postcssConfig.plugins).process(cssResult, {
      from: undefined
    });

    postcssResult.warnings().forEach((warn) => console.warn(warn.toString()));

    totalCss += postcssResult.css;
    await writeOutFile(createOutputFile(filepath), postcssResult.css);
  }
  fs.outputFile(
    path.resolve(cwd, zanDocStyleFile),
    `${docSassDefault}\n${docSassEntries
      .sort()
      .map((entry) => `@import './${entry}';`)
      .join('\n')}`
  );
  writeOutFile('index.css', totalCss);
}

run();

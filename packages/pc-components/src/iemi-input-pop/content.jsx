import { isEqual } from 'lodash';
import propTypes from 'prop-types';
import React from 'react';
import { Button, Input } from 'zent';

import CodeList from './code-list';

class Content extends React.Component {
  static propTypes = { onConfirm: propTypes.func };

  state = {
    iemiList: [],
    inputCode: '',
    prevProps: {}
  };

  static getDerivedStateFromProps(nextProps, state) {
    const { prevProps } = state;
    if (!isEqual(nextProps.value, prevProps.value)) {
      return {
        prevProps: nextProps,
        iemiList: nextProps.value
      };
    }

    return null;
  }

  onAdd = () => {
    const { inputCode, iemiList } = this.state;
    if (!inputCode) {
      return;
    }

    if (iemiList.includes(inputCode)) {
      return;
    }

    this.setState({ iemiList: iemiList.concat(String(inputCode).trim()) });
  };

  onDelete = (iemi) => {
    const { iemiList } = this.state;
    this.setState({ iemiList: iemiList.filter((item) => item !== iemi) });
  };

  onSubmit = () => {
    const { iemiList } = this.state;
    this.props.onConfirm(iemiList);
    this.onClose();
  };

  onClose = () => {
    this.props.triggerPop(false);
  };

  onInputCodeChange = (e) => {
    this.setState({ inputCode: e.target.value });
  };

  render() {
    const { inputCode, iemiList } = this.state;

    return (
      <div className="od-iemi-list">
        <div className="iemi-list__input">
          <Input value={inputCode} onChange={this.onInputCodeChange} />
          <Button type="primary" outline onClick={this.onAdd}>
            添加
          </Button>
        </div>
        <CodeList onDelete={this.onDelete} list={iemiList} />
        <div className="iemi-list__submit">
          <div>
            共计
            {iemiList.length}
            条唯一码
          </div>
          <div>
            <Button type="primary" onClick={this.onSubmit}>
              确定
            </Button>
            <Button onClick={this.onClose}>取消</Button>
          </div>
        </div>
      </div>
    );
  }
}

export default Content;

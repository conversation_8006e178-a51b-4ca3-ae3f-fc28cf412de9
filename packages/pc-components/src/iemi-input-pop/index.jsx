import { isBoolean } from 'lodash';
import React from 'react';
import { Pop } from 'zent';

import Content from './content';

export default class IemiInputPop extends React.Component {
  state = { popVisable: false };

  triggerPop = (value) => {
    const { popVisable } = this.state;
    if (this.props.disabled) {
      return;
    }
    this.setState({ popVisable: isBoolean(value) ? value : !popVisable });
  };

  render() {
    const { onChange, children, value } = this.props;
    const content = <Content onConfirm={onChange} value={value} triggerPop={this.triggerPop} />;

    return (
      <Pop
        content={content}
        className="od-pop"
        visible={this.state.popVisable}
        position="auto-bottom-center"
      >
        <div className="od-trigger" onClick={this.triggerPop}>
          {children}
        </div>
      </Pop>
    );
  }
}

/**
 * 针对 rc 的 UserPrivacyViewButton 进行业务包装
 */
import {
  UserPrivacyViewButton as RawUserPrivacyViewButton,
  UserPrivacyViewButtonWithData as RawUserPrivacyViewButtonWithData
} from '@youzan/retail-components';
import React from 'react';

/**  useUserPrivacyData 需要另外 import, 否则 export 会报错, 怀疑和 babel-plugin-import 有关... */
import { useUserPrivacyData } from '@youzan/retail-components/es/components/use-user-privacy-data';

const IsUserPrivacyViewButtonEnabled = !_global.business.tradeUserPrivacyInfoDisplay;

export const UserPrivacyViewButton = ({ isVisible = true, ...restProps }: any) => {
  return (
    <RawUserPrivacyViewButton
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...restProps}
      isVisible={isVisible && IsUserPrivacyViewButtonEnabled}
    />
  );
};

export const UserPrivacyViewButtonWithData: typeof RawUserPrivacyViewButtonWithData = ({
  isVisible = true,
  ...restProps
}) => {
  return (
    <RawUserPrivacyViewButtonWithData
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...restProps}
      isVisible={isVisible && IsUserPrivacyViewButtonEnabled}
    />
  );
};

// eslint-disable-next-line @youzan/yz-retail/prefer-pascal-case-const
export { useUserPrivacyData };

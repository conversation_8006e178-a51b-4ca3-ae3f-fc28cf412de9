import { Button as <PERSON><PERSON><PERSON><PERSON>, <PERSON> as SamLink } from '@youzan/sam-components';
import cx from 'classnames';
import React from 'react';
import { Pop } from 'zent';

export const BUTTON_LINK = 'button_link';
export const BUTTON = 'operation';
export const LINK = 'link';
export const LINK_BUTTON = 'link_button';
export const TEXT = 'text';

const withDisabledPop = ({ attributes = {}, disabled }, children) => {
  const { reasonDesc } = attributes;

  if (!disabled || !reasonDesc) {
    return children;
  }

  return (
    <Pop trigger="hover" content={reasonDesc}>
      <div style={{ display: 'inline-block' }}>{children}</div>
    </Pop>
  );
};

export const LinkButton = (props) => {
  const { text, onClick, className, disabled, attributes } = props;
  const classnames = cx('link-button', className);
  return withDisabledPop(
    { attributes, disabled },
    <SamLink className={classnames} onClick={onClick} name={text} disabled={disabled}>
      {text}
    </SamLink>
  );
};

export const ButtonLink = (props) => {
  const {
    attributes = {},
    text,
    className,
    buttonType = 'primary',
    outline = true,
    disabled
  } = props;

  return withDisabledPop(
    { attributes, disabled },
    <SamButton
      outline={outline}
      type={buttonType}
      href={attributes.url}
      target="_blank"
      className={className}
      name={text}
      disabled={disabled}
    />
  );
};

export const Button = (props) => {
  const {
    onClick,
    text,
    className,
    attributes = {},
    buttonType = 'primary',
    outline = true,
    disabled,
    loading = false
  } = props;
  return withDisabledPop(
    { attributes, disabled },
    <SamButton
      outline={outline}
      type={buttonType}
      disabled={disabled}
      className={className}
      onClick={onClick}
      name={text}
      loading={loading}
    />
  );
};

export const Link = (props) => {
  const { attributes = {}, text, className, disabled } = props;
  if (disabled) return <span style={{ color: 'darkgray' }}>{text}</span>;
  return (
    <a href={attributes.url} target="_blank" rel="noopener noreferrer" className={className}>
      {text}
    </a>
  );
};

export const Text = (props) => {
  const { className, text } = props;
  return <span className={className}>{text}</span>;
};

export const optTypeMap = {
  [BUTTON]: Button,
  [BUTTON_LINK]: ButtonLink,
  [LINK_BUTTON]: LinkButton,
  [LINK]: Link,
  [TEXT]: Text
};

export const LightweightTypeComponents = new Map([
  [BUTTON_LINK, ButtonLink],
  [LINK, Link],
  [TEXT, Text]
]);

export const displayTypeMap = [TEXT, LINK, BUTTON_LINK];
export const displayOptTypeMap = {
  [BUTTON_LINK]: ButtonLink,
  [LINK]: Link,
  [TEXT]: Text
};

export const TypeButton = ({ type, className, ...props }) => {
  const Component = optTypeMap[type];
  if (!Component) return null;

  return <Component className={cx('button', className)} {...props} />;
};

export default optTypeMap;

import { setUrlDomain } from '@youzan/retail-utils';

/** 打开弹窗 url query key */
const OpenDialogUrlQueryKey = 'open-dialog';

type DialogId = 'business-choice';

/** 前往配送管理-同城配送服务页面 */
export function makeDeliveryLocalDeliveryPageUrl({
  warehouseId,
  tabType,
  openDialog
}: {
  warehouseId: number;
  tabType: 'service';
  openDialog?: DialogId;
}) {
  // eslint-disable-next-line compat/compat
  const searchObj = new URLSearchParams();
  if (openDialog) {
    searchObj.set(OpenDialogUrlQueryKey, openDialog);
  }
  const search = searchObj.toString();

  return setUrlDomain(
    `/fulfillment/delivery#/local-delivery/${warehouseId}/${tabType}${search ? `?${search}` : ''}`,
    'store'
  );
}

const recommendServicekey = `recommend-service_${window._global.kdtId}`;
export const shouldRecommendServiceTipRemind = () => {
  const data = localStorage.getItem(recommendServicekey);
  const day15Timestamp = 15 * 24 * 60 * 60 * 1000;
  const day7Timestamp = 7 * 24 * 60 * 60 * 1000;

  if (data) {
    const { last15Shown, last7Shown, notRemind } = JSON.parse(data);
    const currentTime = new Date().getTime();

    // 15 天内不再提醒，操作十五天内提醒一次;
    if (notRemind) {
      if (currentTime - last15Shown > day15Timestamp) {
        // 如果距离上次弹窗已经超过 15 天，则重新提醒
        localStorage.setItem(
          recommendServicekey,
          JSON.stringify({
            last15Shown: currentTime,
            last7Shown: currentTime,
            notRemind: false
          })
        );
        return true;
      }

      return false;
    }

    if (currentTime - last7Shown > day7Timestamp) {
      // 如果距离上次弹窗已经超过 7 天，可以再次提醒
      localStorage.setItem(
        recommendServicekey,
        JSON.stringify({
          last15Shown,
          last7Shown: currentTime,
          notRemind
        })
      );
      return true;
    }
  } else {
    // 如果没有缓存数据，说明是第一次
    const currentTime = new Date().getTime();
    localStorage.setItem(
      recommendServicekey,
      JSON.stringify({
        last15Shown: currentTime,
        last7Shown: currentTime,
        notRemind: false
      })
    );
    return true;
  }

  return false;
};

export const handleRecommendServiceNotRemind = () => {
  const currentTime = new Date().getTime();
  localStorage.setItem(
    recommendServicekey,
    JSON.stringify({
      last15Shown: currentTime,
      last7Shown: currentTime,
      notRemind: true
    })
  );
};

export const handleReset7DayNotRemind = () => {
  const data = localStorage.getItem(recommendServicekey);
  const currentTime = new Date().getTime();

  if (data) {
    const { last15Shown, notRemind } = JSON.parse(data);
    localStorage.setItem(
      recommendServicekey,
      JSON.stringify({
        last15Shown,
        last7Shown: currentTime,
        notRemind
      })
    );
  } else {
    localStorage.setItem(
      recommendServicekey,
      JSON.stringify({
        last15Shown: currentTime,
        last7Shown: currentTime,
        notRemind: false
      })
    );
  }
};

export const prefixZero = (n: number) => {
  return `0${n}`.slice(-2);
};

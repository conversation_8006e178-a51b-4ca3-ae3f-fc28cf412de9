/* eslint-disable @typescript-eslint/ban-ts-comment */
import { BlankLink, Space } from '@youzan/react-components';
import React, { useEffect } from 'react';
import { DeepPartial } from 'utility-types';
import { InlineLoading, Notify, Radio, RadioGroup, Select } from 'zent';

import { IOrderInfo } from '@youzan/order-domain-definitions/es/order';
import { ChannelAccountType } from '@youzan/order-domain-definitions/es/wx-channel-shop';
import useAsyncFn from '../../../../common/hooks/use-async-fn';
import useMounted from '../../../../common/hooks/use-mounted';
import { useZentCompatSelectProps } from '../../../../common/hooks/use-zent-select';

import {
  AddressTypeValue,
  getDeliveryAddressList,
  getMultiPlatExpressInfos,
  getMultiPlatPageSites,
  getWaybillDeliveryTemplate,
  queryExpressProductTypes,
  queryPageMonthlyCards
} from '../../../../common/features/wx-channel-shop/apis';
import { CooperateType } from '../../../../common/features/wx-channel-shop/apis/types';
import { getMpIdFromOrderInfo } from '../../../../common/features/wx-channel-shop/utils';

import {
  ActivateWxChannelShopLink,
  AddDeliveryCompanyLink,
  CreateWaybillTemplateLink,
  PrinterComponentUsageLink
} from '../../../../common/features/wx-channel-shop/constants';
import { useWxChannelShopPrinter } from '../../../../common/features/wx-channel-shop/use-wx-channel-shop-printer';
import { ExpressContentItem } from '../../../components/express-content-item';
import { ONLY_DZMD, SYSTEM_CALL_TYPE_MAP } from '../../../constants';
import { WxChannelShopLocalExpressData } from '../../types';
import { getExpressSiteLocalExpressData } from '../../utils';
import { alertRechargingExpressSite } from '../alert-recharging-express-site';

export interface ExpressWxChannelShopEWaybillProps {
  orderInfo: IOrderInfo;
  express: WxChannelShopLocalExpressData;
  onPrinterChange: (data: { target: { value: string } }) => void;
  onChangeExpress: (data: DeepPartial<WxChannelShopLocalExpressData>) => void;
}

/**
 * 视频号小店 - 电子面单
 */
export function ExpressWxChannelShopEWaybill({
  orderInfo,
  express = {} as WxChannelShopLocalExpressData,
  onChangeExpress
}: ExpressWxChannelShopEWaybillProps) {
  const mpId = getMpIdFromOrderInfo(orderInfo);
  const salesKdtId = orderInfo.mainOrderInfo.kdtId;

  const [waybillTemplatesState, syncWaybillTemplatesWithDefaultSelected] = useAsyncFn(
    ({ expressId }: { expressId: number }) => {
      return getWaybillDeliveryTemplate({
        salesKdtId,
        channelType: ChannelAccountType.WxChannelShop,
        expressId,
        mpId
      })
        .then((data) => {
          if (data && data.templates.length > 0) {
            const selected = data.templates.find((item) => item.isDefault) || data.templates[0];
            onChangeExpress({
              thirdTemplateId: selected.thirdTemplateId
            });
          }
          return data;
        })
        .catch((err) => {
          Notify.error(err.msg);
          throw err;
        });
    }
  );

  const [deliveryAddressListState, syncDeliveryAddressList] = useAsyncFn(() => {
    return getDeliveryAddressList({
      targetKdtId: salesKdtId,
      addressTypeValues: [AddressTypeValue.Return, AddressTypeValue.Delivery],
      pageNum: 1,
      pageSize: 200
    })
      .then((data) => {
        return data.data || [];
      })
      .catch((err) => {
        Notify.error(err.msg);
        throw err;
      });
  });

  const syncDeliveryAddressListWithDefaultSelected = () => {
    syncDeliveryAddressList().then((addrList) => {
      if (addrList && addrList.length > 0) {
        const selected =
          addrList.find((item) => item.isShipAddress) ||
          addrList.find((item) => item.isDefaultReturnAddress) ||
          addrList[0];
        onChangeExpress({
          senderAddressId: selected.addressId,
          senderAddress: selected
        });
      }
    });
  };

  const { printerListState, syncPrinterList } = useWxChannelShopPrinter();

  const syncPrinterListWithDefaultSelected = () => {
    syncPrinterList().then((data) => {
      if (data.length > 0) {
        onChangeExpress({
          printerName: data[0].name
        });
      }
    });
  };

  const [expressCompanyState, syncExpressCompany] = useAsyncFn(() => {
    return getMultiPlatExpressInfos({
      salesKdtId,
      channelType: ChannelAccountType.WxChannelShop,
      mpId
    }).catch((err) => {
      Notify.error(err.msg);
      throw err;
    });
  });

  const syncExpressCompanyWithDefaultSelected = () => {
    return syncExpressCompany().then((companies) => {
      syncPrinterListWithDefaultSelected();
      syncDeliveryAddressListWithDefaultSelected();
      return companies;
    });
  };

  const [expressSitesState, syncExpressSites] = useAsyncFn(
    ({ expressId }: { expressId: number }) => {
      return getMultiPlatPageSites({
        yzExpressId: expressId,
        salesKdtId,
        channelType: ChannelAccountType.WxChannelShop,
        mpId,
        page: 1,
        pageSize: 200
      })
        .then((data) => {
          return data.items;
        })
        .catch((err) => {
          Notify.error(err.msg);
          throw err;
        });
    }
  );

  const [expressProductTypesState, syncExpressProductTypes] = useAsyncFn(
    ({ expressId }: { expressId: number }) => {
      return queryExpressProductTypes({
        yzExpressId: expressId,
        salesKdtId,
        channelType: ChannelAccountType.WxChannelShop,
        mpId
      })
        .then((data) => {
          return data;
        })
        .catch((err) => {
          Notify.error(err.msg);
          throw err;
        });
    }
  );

  const [expressMonthlyCardsState, syncMonthlyCards] = useAsyncFn(
    ({ expressId }: { expressId: number }) => {
      return queryPageMonthlyCards({
        yzExpressId: expressId,
        salesKdtId,
        channelType: ChannelAccountType.WxChannelShop,
        mpId,
        page: 1,
        pageSize: 200
      })
        .then((data) => {
          return data.items;
        })
        .catch((err) => {
          Notify.error(err.msg);
          throw err;
        });
    }
  );
  const syncExpressSitesWithDefaultSelected = ({ expressId }: { expressId: number }) => {
    return syncExpressSites({ expressId }).then((sites) => {
      if (sites && sites.length > 0) {
        const [firstSite] = sites;
        onChangeExpress(getExpressSiteLocalExpressData(firstSite));
      }
      return sites;
    });
  };

  const syncExpressProductTypesWithDefaultSelected = ({ expressId }: { expressId: number }) => {
    return syncExpressProductTypes({ expressId }).then((sites) => {
      if (sites && sites.length > 0) {
        const [firstSite] = sites;
        onChangeExpress({
          thirdProductTypeId: firstSite.outProductTypeId
        });
      }
      return sites;
    });
  };

  const syncMonthlyCardsWithDefaultSelected = ({ expressId }: { expressId: number }) => {
    return syncMonthlyCards({ expressId }).then((sites) => {
      if (sites && sites.length > 0) {
        const [firstSite] = sites;
        onChangeExpress({
          outWaybillAccountId: firstSite.outWaybillAccountId,
          monthlyCardId: firstSite.monthlyCardId
        });
      }
      return sites;
    });
  };

  /**
   * Select 参数适配
   */

  const expressCompanySelectProps = useZentCompatSelectProps({
    value: express.expressId,
    options:
      expressCompanyState.value?.map((item) => {
        return {
          key: item.expressId,
          text: item.expressName
        };
      }) || [],
    onChange: (value) => {
      const selectedExpressCompany = expressCompanyState.value?.find(
        (item) => item.expressId === value
      );
      onChangeExpress({
        expressId: value,
        outShopId: selectedExpressCompany?.expressAccountInfo.outShopId,
        expressCompany: selectedExpressCompany
      });
    }
  });

  const selectedExpressCompany = expressCompanyState.value?.find(
    (item) => item.expressId === express.expressId
  );

  const selectedExpressSite = expressSitesState.value?.find(
    (item) => item.siteInfo.outSiteId === express.outSiteId
  );

  const expressSiteSelectProps = useZentCompatSelectProps({
    value: express.outSiteId,
    options: expressSitesState.value
      ? expressSitesState.value.map((item) => {
          const text = [
            item.siteInfo.address.province,
            item.siteInfo.address.city,
            item.siteInfo.address.district,
            item.siteInfo.address.street,
            item.siteInfo.address.detailAddress
          ]
            .filter(Boolean)
            .join('');

          return {
            key: item.siteInfo.outSiteId,
            text
          };
        }) || []
      : [],
    onChange: (value) => {
      const selectedExpressSite = expressSitesState.value?.find(
        (item) => item.siteInfo.outSiteId === value
      );

      if (selectedExpressSite) {
        onChangeExpress(getExpressSiteLocalExpressData(selectedExpressSite));
      }
    }
  });

  const expressProductTypesSelectProps = useZentCompatSelectProps({
    value: `${express.thirdProductTypeId}`,
    options: expressProductTypesState.value
      ? expressProductTypesState.value?.map((item) => {
          return {
            key: `${item.outProductTypeId}`,
            text: item.outProductTypeName
          };
        })
      : [],
    onChange: (value) => {
      const selectedExpressSite = expressProductTypesState.value?.find(
        (item) => `${item.outProductTypeId}` === value
      );

      if (selectedExpressSite) {
        onChangeExpress({
          thirdProductTypeId: selectedExpressSite.outProductTypeId
        });
      }
    }
  });

  const expressMonthlyCardsSelectProps = useZentCompatSelectProps({
    value: express.monthlyCardId,
    options: expressMonthlyCardsState.value
      ? expressMonthlyCardsState.value?.map((item) => {
          return {
            key: item.monthlyCardId,
            text: `月结账户 ${item.monthlyCardId}`
          };
        })
      : [],
    onChange: (value) => {
      const selectedExpressSite = expressMonthlyCardsState.value?.find(
        (item) => `${item.monthlyCardId}` === value
      );

      if (selectedExpressSite) {
        onChangeExpress({
          outWaybillAccountId: selectedExpressSite.outWaybillAccountId,
          monthlyCardId: selectedExpressSite.monthlyCardId
        });
      }
    }
  });

  const expressAddrSelectProps = useZentCompatSelectProps({
    value: express.senderAddressId,
    options:
      deliveryAddressListState.value?.map((item) => {
        return {
          key: item.addressId,
          text: `【${item.contactName}】 ${item.address} ${item.mobilePhone || item.telephone}`
        };
      }) || [],
    onChange: (value) => {
      const senderAddress = deliveryAddressListState.value?.find(
        (item) => item.addressId === value
      );
      if (senderAddress) {
        onChangeExpress({
          senderAddressId: value,
          senderAddress
        });
      }
    }
  });

  const waybillTemplatesSelectProps = useZentCompatSelectProps({
    value: express.thirdTemplateId,
    options:
      waybillTemplatesState.value?.templates.map((item) => {
        return {
          key: item.thirdTemplateId,
          text: item.templateName
        };
      }) || [],
    onChange: (value) => {
      onChangeExpress({
        thirdTemplateId: value
      });
    }
  });

  const printerSelectProps = useZentCompatSelectProps({
    value: express.printerName,
    options:
      printerListState.value?.map((item) => {
        return {
          key: item.name,
          text: item.name
        };
      }) || [],
    onChange: (value) => {
      onChangeExpress({
        printerName: value
      });
    }
  });

  /**
   * Effect
   */

  useMounted(() => {
    syncExpressCompanyWithDefaultSelected();
  });

  /** 快递公司变化联动 */
  useEffect(() => {
    if (selectedExpressCompany) {
      if (selectedExpressCompany?.cooperateType === CooperateType.Direct) {
        syncExpressProductTypesWithDefaultSelected({ expressId: selectedExpressCompany.expressId });
        syncMonthlyCardsWithDefaultSelected({ expressId: selectedExpressCompany.expressId });
      } else {
        syncExpressSitesWithDefaultSelected({ expressId: selectedExpressCompany.expressId });
      }
      syncWaybillTemplatesWithDefaultSelected({ expressId: selectedExpressCompany.expressId });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedExpressCompany?.expressId]);

  let expressCompanyContentNode;
  if (expressCompanyState.isLoading) {
    expressCompanyContentNode = <InlineLoading loading icon="circle" />;
  } else {
    expressCompanyContentNode = !expressCompanyState.value?.length ? (
      <Space>
        暂未开通
        <BlankLink href={ActivateWxChannelShopLink}>去小店开通</BlankLink>
      </Space>
    ) : (
      <>
        <Space>
          <Select
            {...expressCompanySelectProps}
            multiple={false}
            placeholder="请选择快递公司"
            loading={expressCompanyState.isLoading}
          />
          <BlankLink href={AddDeliveryCompanyLink}>添加服务商</BlankLink>
        </Space>
      </>
    );
  }

  const expressCompanyNode = (
    <ExpressContentItem label="快递公司">{expressCompanyContentNode}</ExpressContentItem>
  );

  if (!express.expressId) {
    return expressCompanyNode;
  }

  return (
    <>
      {expressCompanyNode}

      {selectedExpressCompany?.cooperateType === CooperateType.Direct ? (
        <>
          <ExpressContentItem label="产品类型">
            <Space>
              <Select
                {...expressProductTypesSelectProps}
                multiple={false}
                placeholder="请选择产品类型"
              />
            </Space>
          </ExpressContentItem>
          <ExpressContentItem label="月结账号">
            <Space>
              <Select
                {...expressMonthlyCardsSelectProps}
                multiple={false}
                placeholder="请选择月结账号"
              />
            </Space>
          </ExpressContentItem>
        </>
      ) : (
        <ExpressContentItem label="物流网点">
          <Space direction="vertical" size="small">
            <Select
              {...expressSiteSelectProps}
              multiple={false}
              placeholder="请选择物流网点"
              loading={expressSitesState.isLoading}
            />
            {selectedExpressCompany && selectedExpressSite ? (
              <Space>
                <span>
                  电子面单库存：{selectedExpressSite.siteAccountInfo.balanceInfo?.available}
                </span>
                <a
                  style={{ color: '#155bd4' }}
                  onClick={() => {
                    alertRechargingExpressSite({
                      expressCompany: selectedExpressCompany,
                      expressSite: selectedExpressSite
                    });
                  }}
                >
                  去充值
                </a>
              </Space>
            ) : null}
          </Space>
        </ExpressContentItem>
      )}

      <ExpressContentItem label="发货地址">
        <Space>
          <Select {...expressAddrSelectProps} multiple={false} placeholder="请选择发货地址" />
          <a style={{ color: '#155bd4' }} onClick={() => syncDeliveryAddressList()}>
            刷新
          </a>
        </Space>
      </ExpressContentItem>

      <ExpressContentItem label="发货类型">
        <RadioGroup value={ONLY_DZMD}>
          <Radio value={ONLY_DZMD}>{SYSTEM_CALL_TYPE_MAP[ONLY_DZMD]}</Radio>
        </RadioGroup>
      </ExpressContentItem>

      <ExpressContentItem label="面单模板">
        <Space direction="vertical" size="small">
          <Select
            {...waybillTemplatesSelectProps}
            multiple={false}
            placeholder="请选择面单模板"
            loading={waybillTemplatesState.isLoading}
            disabled={!waybillTemplatesState.value?.templates.length}
          />
          {!waybillTemplatesState.isLoading && !waybillTemplatesState.value?.templates.length ? (
            <Space>
              <span>该快递服务商暂未设置面单模板。去小店创建</span>
              <BlankLink href={CreateWaybillTemplateLink}>去小店创建</BlankLink>
            </Space>
          ) : null}
        </Space>
      </ExpressContentItem>

      <ExpressContentItem label="打印机">
        <Space direction="vertical" size="small">
          <Space>
            <Select
              {...printerSelectProps}
              placeholder="请选择打印机"
              loading={printerListState.isLoading}
              disabled={!printerListState.value?.length}
            />
            <a style={{ color: '#155bd4' }} onClick={() => syncPrinterList()}>
              刷新
            </a>
          </Space>
          {!printerListState.isLoading && !printerListState.value?.length ? (
            <Space>
              <span>未识别到可选择的打印机。</span>
              <BlankLink href={PrinterComponentUsageLink}>查看打印组件使用教程</BlankLink>
            </Space>
          ) : null}
        </Space>
      </ExpressContentItem>
    </>
  );
}

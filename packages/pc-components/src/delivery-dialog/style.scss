@import '../../style';

$label-width: 100px;
$express-list-width: 550px;

.od-express-dialog {
  width: 800px;

  .warning {
    margin-bottom: 10px;
  }

  .zent-dialog-r-title {
    margin-bottom: 10px;
  }
}

.od-express-content {
  .amount-content {
    margin-top: 10px;
  }

  .zent-tabs {
    margin-bottom: 10px;
  }

  .pl90 {
    padding-left: 90px;
  }

  .pl100 {
    padding-left: 100px;
  }

  .express-way-bill-fee-line {
    margin-left: 16px;
    &_label {
      width: 100px;
      text-align: right;
    }
    &_content {
      margin-left: 10px;
      &-price {
        font-size: 20px;
        color: #d42f15;
      }
    }
    &_balance-warn {
      margin-left: 80px;
      color: #d42f15;
    }

    &_subsidy {
      color: #d42f15;
      border-color: #d42f15;
      background-color: #fff;

      border: 1.5px solid;
      padding: 0px 4px;
      border-radius: 3px;
  
      font-size: 14px;
      line-height: 1.6;

      width: fit-content;
      margin-left: 80px;
      margin-top: 4px;
    }
  }

  .express-content-scroller {
    max-height: 600px;
    overflow: auto;
  }

  .express-content-item {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;

    &:last-child {
      margin-bottom: 0;
    }

    .text-one-line {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .error-info {
      color: #df4545;
      font-size: 12px;
      width: 100%;
    }

    .error-field {
      .zent-select-text {
        border-color: #df4545;
      }
    }

    .pick-time-field {
      display: flex;
      .zent-select {
        width: 160px;
      }
    }

    .attribute-field {
      margin-top: 15px;
    }

    .city-delivery-text {
      margin-left: 8px;
      position: relative;
      top: -1px;
    }

    &.actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 16px;

      .disable-desc {
        margin-right: 10px;
      }

      .agree-protocol {
        margin-right: 10px;
        display: flex;
        align-items: center;

        .zent-checkbox-wrap {
          margin-right: 5px;
        }
      }
    }

    &.spread {
      margin-top: 15px;
    }

    &__label {
      flex-basis: $label-width;
      color: $color-text-primary;
    }

    &__express-company {
      &-form {
        .zent-form-label {
          flex-basis: 80px;
        }
      }

      &-container {
        width: $express-list-width;
        max-height: 236px;
        overflow-y: auto;
        display: flex;
        padding: 12px;
        background: #ffffff;
        border: 1px solid #e0e0e0;
        box-sizing: border-box;
        align-content: flex-start;
        flex-wrap: wrap;
        padding-right: 5px;
        padding-bottom: 0;
      }

      &-item {
        width: 166px;
        height: 62px;
        background-color: #f7f7f7;
        font-size: 14px;
        font-weight: bold;
        margin-right: 7px;
        padding-left: 8px;
        padding-top: 4px;
        box-sizing: border-box;
        flex-shrink: 0;
        position: relative;
        border-radius: 2px;
        border: 1px solid #f7f7f7;
        cursor: pointer;
        margin-bottom: 12px;

        .title {
          display: flex;
          align-items: center;
          line-height: 30px;
        }

        .tips {
          color: #999;
          font-size: 12px;
          transform: scale(0.92);
          font-weight: normal;
          margin-left: -7px;
        }

        .text-price {
          margin-right: 2px;
          color: #ff0404;
          font-size: 12px;
          font-weight: 700;
          &-int {
            font-size: 18px;
          }
          &-float {
            font-size: 16px;
          }
        }
        .text-origin-price {
          text-decoration: line-through;
        }

        .recommend {
          width: 48px;
          height: 18px;
          font-size: 12px;
          line-height: 18px;
          color: #d42f15;
          margin-left: 8px;
          border: 1px solid #d42f15;
          padding: 0 3px;
          border-radius: 3px;
          font-weight: normal;
        }
      }

      &-item-checked {
        border: 1px solid #155bd4;
      }

      &-item-checked::after {
        content: '';
        display: inline-block;
        position: absolute;
        width: 30px;
        height: 30px;
        bottom: -1px;
        right: -1px;
        background: url(https://img01.yzcdn.cn/upload_files/2020/09/22/FuT1oDUt4XIF8g5jv4d-LBy2nhda.png)
          no-repeat;
        background-size: 30px auto;
      }

      &-action {
        box-sizing: border-box;
        width: $express-list-width;
        border: 1px solid #e0e0e0;
        border-top: none;
        line-height: 30px;
        a {
          margin-left: 12px;
          cursor: pointer;
        }
      }

      &-protocol {
        box-sizing: border-box;
        width: $express-list-width;
        padding: 10px 16px;
        display: flex;
        align-items: flex-start;
        border: 1px solid #e0e0e0;
        border-top: none;
        background-color: #f7f7f7;
        .zent-checkbox {
          margin-top: 2px;
        }
      }
    }

    &__value {
      .zent-form__control-label {
        width: 100px;
      }

      .zent-form__control-group {
        margin-bottom: 0px;
      }

      &.weight-input {
        display: flex;

        .zent-number-input-wrapper {
          margin-right: 10px;
        }

        .after-input-text {
          margin-left: 10px;
        }
      }

      .express-companys {
        width: 180px;
      }

      .express-address {
        width: 340px;
      }

      &.selffetch {
        display: flex;
        align-items: center;
      }

      .express-way-bill-cainiao-printer-tips {
        width: 450px;
        font-size: 12px;
        line-height: 18px;
        margin: 4px 0 0 100px;
      }
    }

    .express-content-item__value {
      flex: 1;
    }

    &_goods-scan-input {
      display: inline-block;
      vertical-align: middle;
    }
  }


  .od-new-electron-waybill-alert {
    margin: 0 100px 15px;
  }

  .express-content-extra {
    margin-left: 90px;

    .express-content-item__label {
      text-align: left;
    }
  }

  .express-type__item {
    vertical-align: middle;
  }

  .express-weight {
    margin-bottom: 15px;
    line-height: 30px;

    .express-content-item:last-child {
      margin-bottom: 0;
    }

    .check-address {
      color: $color-warn;
      font-size: $font-size-small;
      padding-left: 10px;
    }
  }

  .postage-info {
    margin-bottom: 15px;
    padding-left: 10px;
    font-size: $font-size-small;
  }

  .orange {
    color: $color-warn;
  }

  .goods-iemi.enabled,
  .goods-lot-code.enabled {
    color: $color-link;
    cursor: pointer;
  }

  .goods-sku {
    color: $color-text-light-primary;
  }

  .delivery-extra {
    &-content {
      display: flex;
      flex-direction: column;

      &__label {
        flex-basis: $label-width;
      }

      &__value {
        flex: 1;
        margin-top: 5px;
      }

      &__info {
        width: 100%;
        margin-left: 100px;
        color: $color-text-secondary;
        margin-top: 4px;
        display: flex;
        align-items: center;
      }

      &__desc {
        margin: 10px 0;
        padding: 14px;
        background-color: #f7f8fa; /* stylelint-disable-line sh-waqar/declaration-use-variable */
      }
      .delivery-price {
        display: flex;
        align-items: normal;
        .delivery-extra-content__value_1 {
          display: flex;

          .exception {
            color: #d42f15;
          }

          .error-span {
            margin-left: 4px;
            padding: 0 4px;
            color: rgb(237, 106, 24);
            border: 1px solid rgb(237, 106, 24);
            border-radius: 2px;
          }
        }
      }

      .input-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        &.auto-call {
          align-items: start;
        }

        .zent-radio-group {
          display: inline-flex;
          align-items: center;

          .zent-radio-wrap,
          .zent-radio + span {
            display: inline-flex;
            align-items: center;

            .zent-input-wrapper {
              margin-right: 10px;
            }
          }
        }

        .delivery-companys {
          width: 180px;
        }

        .delivery-baojia {
          width: 300px;
        }

        .delivery-custom-weight {
          width: 130px;
        }

        .delivery-extra-content__value {
          display: flex;
          flex-direction: column;

          .tips {
            color: $color-warn;
          }

          .exception {
            color: #d42f15;
          }
          &__pop-info {
            margin-left: 4px;
            height: 24px;
            line-height: 24px;
            .zent-pop-v2-inner {
              width: 840px;
            }
          }
        }

        .delivery-extra-content__value_1 {
          display: flex;

          .exception {
            color: #d42f15;
          }

          .error-span {
            margin-left: 4px;
            padding: 0 4px;
            color: rgb(237, 106, 24);
            border: 1px solid rgb(237, 106, 24);
            border-radius: 2px;
          }
        }

        &:last-child {
          flex: 1;
          margin-bottom: 0;
        }
      }

      .dist-Weight {
        .delivery-extra-content__value {
          flex-direction: row;
          align-items: center;
        }
      }

      .delivery-service {
        .delivery-extra-content__value {
          flex-direction: row;
          align-items: center;
        }
        .zent-checkbox-wrap {
          margin-right: 8px;
        }
      }
    }

    .delivery-extra-autoCall {
      color: $color-success;
      margin-top: 8px;
    }

    .local-delivery-extra__ai-select {
      align-items: flex-start;

      .ai-select__text:last-child {
        margin-top: 8px;
      }

      .ai-select__link {
        margin-left: 100px;
        margin-top: 0;
      }
    }
  }

  .delivery-extra-content__value {
    display: flex;

    .exception {
      color: #d42f15;
    }

    .error-span {
      margin-left: 4px;
      padding: 0 4px;
      color: rgb(237, 106, 24);
      border: 1px solid rgb(237, 106, 24);
      border-radius: 2px;
    }
  }

  .zent-grid {
    margin-bottom: 20px;

    &-tr__expanded td {
      width: 100%;
      padding: 0;
    }

    col:first-child,
    th:first-child,
    td:first-child {
      display: none;
    }

    .zent-grid-td {
      border-right: 0;
    }

    .express-table-expanded {
      border-bottom: 1px solid $border-color-base;
      background-color: $color-white;

      &.indent {
        padding-left: 20px;
      }

      &__content {
        margin: 0;
        padding-left: 45%;
      }

      &__item {
        display: flex;
        align-items: center;
        padding: 10px 16px;
      }
    }
  }

  .zent-table {
    .tr--expanded {
      display: flex !important;
      background-color: $color-white;

      .td {
        width: 100%;
        padding: 0;
      }
    }

    .expanded-item {
      display: none;
    }

    .cell:nth-child(2) {
      padding-left: 10px;
    }

    .cell.cell--selection {
      padding-left: 34px;
    }

    .express-table-expanded {
      margin: 0;
      padding-left: 45%;

      &__item {
        display: flex;
        align-items: center;
        padding: 10px 0 10px;
      }
    }
  }

  .spread-content {
    overflow: hidden;

    .spread-info {
      float: left;
      margin-right: 13px;
    }
  }

  .spread-price {
    color: $color-alert;
  }
}

.mb5 {
  margin-bottom: 5px;
}

.od-dialog-goods-info {
  padding: 0;
}

.od-self-fetch-margin {
  margin-left: 8px;
}

.select-custom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .select-custom-btn {
    font-size: $font-size-base;
    color: $color-b4;
  }
  .select-custom-text {
    margin-left: 8px;
    font-size: $font-size-base;
    color: $color-n6;
  }
}

.text-one-line {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-gray {
  font-size: 12px !important;
  color: #9a9a9a;
}

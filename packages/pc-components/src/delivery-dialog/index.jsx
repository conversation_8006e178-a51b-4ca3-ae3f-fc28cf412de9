/* eslint-disable react/jsx-props-no-spreading */

import { ExpressType } from '@youzan/order-domain-definitions';
import {
  getQueryFromHash,
  IsLiteOnlineStoreManager,
  shouldOpenFulfillDialog
} from '@youzan/order-domain-utils';
import { setUrlDomain } from '@youzan/retail-utils';
import { get } from 'lodash';
import React from 'react';
import { Dialog, Notify } from 'zent';

import BeforeAfterActionHoc from '../before-after-action';
import { optTypeMap } from '../type-map';

import * as api from './api';
import { EXPRESS_CHANNEL_KEY_NAME, MaxGoodsWeight } from './constants';
// eslint-disable-next-line import/no-named-as-default, import/no-named-as-default-member
import ExpressContent from './express-content';

import { defaultOperation } from './default';

// 履约单发货(发货单发货)
class DeliveryDialog extends React.Component {
  state = {
    loading: false,
    activityTab: 0,
    selectItemIds: [],
    storeId: 0,
    showDialog: false,
    fulfillOrderList: [{}],
    deliveryWindowTips: {},
    orderMark: '',
    storeKdtId: 0,
    localChannels: [],
    /** 电子面单服务店铺信息 */
    electronWayBillServiceKdtIdMap: {}
  };

  fetchLocalChannels = (params) => {
    const lastChooseChannel = window.localStorage.getItem(EXPRESS_CHANNEL_KEY_NAME);
    if (lastChooseChannel) {
      params.last_choose_channel = lastChooseChannel;
    }

    api
      .fetchLocalChannels(params)
      .then((res) => {
        const data = res?.localChannelVOS || [];
        this.setState({ localChannels: data });
      })
      .catch((e) => {
        Notify.error(e.msg);
      });
  };

  fetchExpressInfo = async () => {
    const deliveryNo = get(this.props, 'options.orderInfo.fulfillOrder.deliveryNo', '');
    const orderNo = get(this.props, 'options.orderInfo.mainOrderInfo.orderNo', '');
    const expressType = get(this.props, 'options.orderInfo.mainOrderInfo.expressType', '');
    const queryMap = getQueryFromHash();

    /** 标识是否来自于退款页面 */
    const isFromRefund = this.props.fromRefund || queryMap.fromRefund;

    const params = deliveryNo
      ? {
          orderNo,
          deliveryNo,
          sourceType: isFromRefund ? 2 : 1
        }
      : { orderNo, sourceType: isFromRefund ? 2 : 1 };

    this.setState({ loading: true });

    try {
      // 校验电子面单功能
      // 在这里加的原因是，fetchDeliveryWindowInfo会直接获取电子面单的状态
      await api.checkShopElectronWayBill();
      // eslint-disable-next-line no-empty
    } catch (err) {}

    Promise.all([api.fetchDeliveryWindowInfo(params), api.getElectronWayBillServiceKdtId(params)])
      .then(
        ([
          { data: fulfillOrderList = [], selfFetchId, ...others } = {},
          electronWayBillServiceKdtIdMap
        ]) => {
          const { warehouseId } = fulfillOrderList[0];
          if (fulfillOrderList[0].channels) {
            fulfillOrderList[0].channels.unshift({
              deliveryChannel: '',
              deliveryChannelDesc: '请选择',
              initialWeight: 1000,
              maxWeight: MaxGoodsWeight,
              status: 2,
              supportInsurance: false
            });
          }
          this.setState({
            fulfillOrderList,
            orderNo,
            electronWayBillServiceKdtIdMap,
            ...others
          });
          if (expressType === ExpressType.City) {
            this.fetchLocalChannels({ ...params, warehouseId: fulfillOrderList[0]?.warehouseId });
          }
          return api.querySelfFetchSetting({ subKdtId: warehouseId, selfFetchId });
        }
      )
      .then((res) => {
        const { verifyType } = res;
        this.setState({
          verifyType
        });
      })
      .catch((e) => {
        Notify.error(e.msg);
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  };

  // 单独刷新服务商的接口，刷新价格等
  refreshDeliveryChannel = ({
    deliveryChannel,
    estimateDeliveryTime,
    appId,
    getCaclPostageParams
  }) => {
    const deliveryNo = get(this.props, 'options.orderInfo.fulfillOrder.deliveryNo', '');
    const orderNo = get(this.props, 'options.orderInfo.mainOrderInfo.orderNo', '');
    const queryMap = getQueryFromHash();

    /** 标识是否来自于退款页面 */
    const isFromRefund = this.props.fromRefund ?? queryMap.fromRefund;

    const params = deliveryNo
      ? {
          orderNo,
          deliveryNo,
          sourceType: isFromRefund ? 2 : 1
        }
      : { orderNo, sourceType: isFromRefund ? 2 : 1 };

    this.setState({ loading: true });
    const { distWeight } = getCaclPostageParams();
    const lastChooseChannel = window.localStorage.getItem(EXPRESS_CHANNEL_KEY_NAME);

    api
      .refreshLocalChannel({
        ...params,
        last_choose_channel: lastChooseChannel,
        existChannels: this.state.localChannels, // 需要把上一次列表传给后端，
        distWeight,
        appId,
        estimateDeliveryTime,
        deliveryChannelId: deliveryChannel,
        warehouseId: get(this.state, 'fulfillOrderList[0]', {}).warehouseId
      })
      .then((res) => {
        this.setState({ localChannels: res?.localChannelVOS || [] });
      })
      .catch((e) => {
        Notify.error(e.msg);
      })
      .finally(() =>
        this.setState({
          loading: false
        })
      );
  };

  openDialog = (data) => {
    this.fetchExpressInfo();

    this.triggerDialog(true);
    if (data) {
      setTimeout(() => this.content.setExtraParams(data));
    }
  };

  triggerDialog = (showDialog, cb) => {
    const newState = {
      showDialog,
    }

    if (!showDialog) {
      newState.fulfillOrderList = [{}]
    }

    this.setState(newState, () => {
      cb && cb();
    });
  };

  getDialogTitle = () => {
    const firstItem = get(this.state, 'fulfillOrderList[0]', {});
    if (!firstItem.multiPeriodDeliveryInfo) {
      return '商品发货';
    }
    const { deliveryTime = '', period } = firstItem.multiPeriodDeliveryInfo;

    return (
      <p>
        商品发货(第
        <span className="orange">{period}</span>
        期发货，预计于
        <span className="orange">{deliveryTime}</span>
        送达)
      </p>
    );
  };

  getLiteTreeData = () => {
    api
      .getLiteStore()
      .then(({ liteStore = [] }) => {
        if (liteStore.length !== 0) {
          const { storeKdtId } = liteStore[0];
          this.setState({ storeKdtId });
        }
      })
      .catch(({ msg }) => Notify.error(msg || '获取同城网店失败'));
  };

  componentDidMount() {
    if (shouldOpenFulfillDialog()) {
      this.openDialog();
    }
    IsLiteOnlineStoreManager && this.getLiteTreeData();
  }

  render() {
    const {
      orderNo,
      storeId,
      isFreeBuy,
      deliveryWindowTips,
      supportMoreDistOrder,
      verifyType,
      channelType,
      orderMark,
      storeKdtId,
      electronWayBillServiceKdtIdMap
    } = this.state;
    const {
      operation = {},
      options = {},
      beforeDoExpress,
      fromRefund,
      onlyNeedDialog
    } = this.props;

    const OptTypeCpm = BeforeAfterActionHoc(optTypeMap[operation.type]);
    const operationWithDefault = Object.assign(defaultOperation, operation);

    // 传一下[订单类型]和[配送渠道类型（如蜂鸟、闪送）]，后面会有判断逻辑
    const orderType = get(options, 'orderInfo.mainOrderInfo.orderType');
    const channelTypeFromList = get(options, 'orderInfo.mainOrderInfo.channelType');
    const orderInfo = get(options, 'orderInfo');
    return (
      <div>
        <Dialog
          visible={this.state.showDialog}
          maskClosable={false}
          onClose={() => this.triggerDialog(false)}
          title={this.getDialogTitle()}
          className="od-express-dialog"
        >
          {this.state.showDialog && (
            <ExpressContent
              loading={this.state.loading}
              deliveryWindowTips={deliveryWindowTips}
              orderNo={orderNo}
              placeType={this.props.placeType}
              orderInfo={orderInfo}
              onSuccess={(data) => {
                if (window.location.pathname.match('/fulfilldetail') && data?.newFulfillNo) {
                  // 履约单详情页改派发货成功后跳到新履约单
                  window.location.href = setUrlDomain(
                    `/v2/order/fulfilldetail#/?fulfillNo=${data.newFulfillNo}`,
                    'store'
                  );
                  window.location.reload();
                } else {
                  options.reload && options.reload();
                }
              }}
              tabRef={(content) => {
                this.content = content;
              }}
              onClose={() => this.triggerDialog(false)}
              data={this.state.fulfillOrderList}
              electronWayBillServiceKdtIdMap={electronWayBillServiceKdtIdMap}
              storeId={storeId}
              orderType={orderType}
              // 优先取列表中的 channelType ，防止频繁变化引起闪动
              channelType={channelTypeFromList || channelType}
              supportMoreDistOrder={supportMoreDistOrder}
              isFreeBuy={isFreeBuy}
              beforeDoExpress={beforeDoExpress}
              fromRefund={fromRefund}
              verifyType={verifyType}
              orderMark={orderMark}
              storeKdtId={storeKdtId}
              reload={this.fetchExpressInfo}
              refreshDeliveryChannel={this.refreshDeliveryChannel}
              fetchLocalChannels={this.fetchLocalChannels}
              localChannels={this.state.localChannels}
            />
          )}
        </Dialog>
        {!onlyNeedDialog && (
          <OptTypeCpm
            actionName="OpenDialog"
            {...operationWithDefault}
            onClick={() => this.openDialog()}
            beforeOpenDialog={this.props.beforeOpenDialog}
          />
        )}
      </div>
    );
  }
}

export { defaultOperation };
export default DeliveryDialog;

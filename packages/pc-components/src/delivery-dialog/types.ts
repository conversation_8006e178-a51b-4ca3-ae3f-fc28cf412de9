export interface LocalExpressData {
  expressNo: string;
  expressId: number;
  expressName: string;
  sendType: string;
  weight: string;
  expressAddrList: any[];
  sendTypes: any[];
  printerChannel: string;
  printerDeviceNo: string;
  printerKey: string;
  auditNo: string;
  postage: number;
  isCashPledgeEnough: boolean;
  startTime: string;
}

/** 服务商开通状态 */
export enum AuditStatus {
  /** 未申请过 */
  NoAudit,
  /** 待审核 */
  Pending,
  /** 开通成功 */
  Success,
  /** 开通失败 */
  Fail,
  // 其他状态码表示 待审核
  /** 没有订购 */
  NoOrder = 5
}

/** 同城配送渠道 */
export enum DeliveryChannel {
  /** 达达 */
  Dada = 1,
  /** 蜂鸟 */
  FengNiao = 2,
  /** 点我达 */
  DianWoDa = 3,
  /** 顺丰同城 */
  ShunFeng = 4,
  /** 美团 */
  MeiTuan = 5,
  /** 闪送 */
  ShanSong = 6,
  /** 达达自结算 */
  DadaSettlement = 11,
  /** 顺丰自结算 */
  ShunFengSettlement = 12,
  /** 蜂鸟自结算 */
  FengNiaoSettlement = 13,
  /** 餐道聚合配送 */
  CanDao = 14,
  /** 智选服务商 */
  IntelligentDeliveryChannel = 100
}

export enum DeliveryPlaceType {
  /** 订单发货 */
  Order = 'order',
  /** 履约发货 */
  Fulfillment = 'fulfillment'
}

// 电子面单的版本枚举
export enum WaybillVersionEnum {
  /** 老版本 */
  Old = 1,
  /** 新版本 */
  New = 2
}

// 快递公司结算模式： 0 官方结算 1 自结算
export enum PaymentTypeEnum {
  /** 官方结算 */
  Authority = 0,
  /** 自结算 */
  Oneself = 1
}

// 有赞寄件加入状态
// eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
export enum YZShippingStatusEnum {
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  WAIT_JOIN = 'WAIT_JOIN', // 待开通
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  JOINED = 'JOINED', // 已开通
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  SUSPEND = 'SUSPEND' // 已暂停
}
import { cloneDeep } from 'lodash';

export const defaultOperation = {
  code: 'delivery',
  type: 'button',
  buttonType: 'primary',
  outline: false,
  text: '发货'
};

export const defaultExpressData = {
  localDelivery: {
    channel: '',
    tip: '', // 暂时没有
    distWeight: ''
  },
  selfFetch: {
    selfFetchNo: ''
  },
  express: {
    expressNo: '',
    expressId: '',
    expressName: '',
    sendType: '',
    weight: '',

    // 发货地址列表
    expressAddrList: [],

    // 发货类型(电子面单还是上门取件)
    sendTypes: [],

    // 打印机型号
    printerChannel: '',
    // 设备编号
    printerDeviceNo: '',
    // 设备密钥
    printerKey: '',

    // 审核单编号
    auditNo: '',

    // 快递运费
    postage: 0,

    /** 产品类型 */
    thirdProductTypeId: 0,
    /** 月结账号 */
    monthlyCardId: '',

    // 保证金是否足够
    isCashPledgeEnough: true,

    // 取件开始时间
    startTime: '',
    // 有赞寄件信息
    YZShoppingInfo: {
      waitJoin: true,
      joined: false,
      suspended: false,
      overdueFee: 0,
      overdueLimitFee: 200
    }
  },
  localPostage: 0
};

export const defaultState = {
  ...cloneDeep(defaultExpressData),

  // 同城配送
  calcWeightType: 'default',

  // 第三方同城运费
  localPostage: 0,

  // 第三方同城配送禁用状态及文案
  deliveryDisabledDesc: '',

  // 打印机设备异常文案
  printerErrDesc: ''
};

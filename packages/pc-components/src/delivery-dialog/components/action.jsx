import { PRINT_BIZ_PICKING } from '@youzan/order-domain-definitions';
import { BlankLink } from '@youzan/react-components';
import { setUrlDomain } from '@youzan/retail-utils';
import { Button as SamButton } from '@youzan/sam-components';
import React, { useState } from 'react';
import { Button, Checkbox } from 'zent';
import { WaybillVersionEnum } from '../types';
import { SEND_TYPE_CODE } from '../constants';

const Action = ({
  disabled,
  printDisabled,
  onClick,
  onPrintReceipt,
  disabledDesc,
  expressText,
  isAgreeProtocol,
  isShowSFProtocol,
  sending,
  onChange,
  loading,
  footerExtra,
  waybillVersion,
  expressType
}) => {
  const [printing, setPrinting] = useState(false);
  const protocolLink = setUrlDomain('/forum.php?mod=viewthread&tid=670164', 'bbs');
  const printPickReceipt = () => {
    setPrinting(true);
    onPrintReceipt(PRINT_BIZ_PICKING).finally(() => {
      setPrinting(false);
    });
  };

  return (
    <div className="express-content-item actions">
      {disabled && disabledDesc && <span className="red disable-desc">{disabledDesc}</span>}
      <p>{waybillVersion === WaybillVersionEnum.New && footerExtra ? footerExtra : null}</p>
      {waybillVersion === WaybillVersionEnum.Old && isShowSFProtocol && (
        <span className="agree-protocol">
          <Checkbox checked={isAgreeProtocol} onChange={onChange} />
          同意 <BlankLink href={protocolLink}>顺丰快件运单契约条款</BlankLink>
        </span>
      )}
      {(expressType === SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL
        ? waybillVersion === WaybillVersionEnum.Old
        : true) &&
        !printDisabled && (
          <SamButton onClick={printPickReceipt} loading={printing} name="打印拣货小票">
            打印拣货小票
          </SamButton>
        )}
      <Button onClick={onClick} type="primary" loading={sending || loading} disabled={!!disabled}>
        {expressText}
      </Button>
    </div>
  );
};

export default Action;

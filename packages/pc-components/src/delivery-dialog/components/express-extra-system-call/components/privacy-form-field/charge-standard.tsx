import React from 'react';
import { Grid } from 'zent';

const dataSource = [
  {
    orderCount: '0-50单',
    price: '0.10',
  },
  {
    orderCount: '51-100单',
    price: '0.09',
  },
  {
    orderCount: '101-500单',
    price: '0.08',
  },
  {
    orderCount: '501-1000单',
    price: '0.07',
  },
  {
    orderCount: '1001单以上',
    price: '0.06',
  },
];

const columns = [
  {
    title: '日均发货订单数',
    width: '50%',
    name: 'orderCount',
  },
  {
    title: '每笔服务费（元）',
    width: '50%',
    name: 'price',
  },
];

const ChargeStandard = () => {
  return <Grid datasets={dataSource} columns={columns} />;
};

export default ChargeStandard;

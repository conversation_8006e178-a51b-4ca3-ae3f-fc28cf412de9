import retailAjax from '@youzan/retail-ajax';

interface ICanOpenPrivacy {
  show: boolean;
}

// 检查是否可开通隐私面单
export const checkCanOpenPrivacy = (data: { pageSource: string }): Promise<ICanOpenPrivacy> => {
  const url = `/v4/trade/electron-way-bill/can-open-privacy.json`;
  return retailAjax({
    method: 'GET',
    url,
    data
  });
};

// 申请开通隐私面单
export const applyOpenPrivacy = (data: { source: string }) => {
  const url = `/v4/assets/api/security/privacy-waybill/apply`;
  return retailAjax({
    method: 'GET',
    url,
    data
  });
};

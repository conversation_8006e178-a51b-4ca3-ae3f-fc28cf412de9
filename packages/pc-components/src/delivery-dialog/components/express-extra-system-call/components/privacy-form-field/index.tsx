/* eslint-disable @youzan/domain/forbid-hardcode-domain-name */
import React, { useEffect, useState } from 'react';
import { Form } from '@zent/compat';
import PopContainer from '../pop-container';
import ChargeStandard from './charge-standard';
import { checkCanOpenPrivacy } from './api';

const { FormCheckboxField } = Form;

interface IProps {
  pageSource: string;
  width?: number;
  position?: string;
  onCheckedChange?: (value: boolean) => void;
}

const PrivacyFormField = (props: IProps) => {
  const { pageSource, width = 526, position = 'top-right', onCheckedChange } = props;
  const [isShow, setIsShow] = useState(false);

  useEffect(() => {
    checkCanOpenPrivacy({ pageSource }).then((data) => {
      setIsShow(data.show);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!isShow) {
    return null;
  }
  return (
    <FormCheckboxField
      name="allowOpenPrivacy"
      value={false}
      className="privacy-form-field"
      label="隐私面单："
      onChange={(value) => {
        const {
          target: { checked },
        } = value;
        onCheckedChange && onCheckedChange(checked);
      }}
      helpDesc={
        <ul className="privacy-help-desc" style={{ width }}>
          <li>
            服务协议请查看
            <a
              href="https://www.youzan.com/intro/rule/detail?alias=2eusz2s0&pageType=rules"
              rel="noreferrer"
              target="_blank"
              className="text-pointer"
            >
              《隐私面单服务协议》
            </a>
          </li>
          <li>
            <span className="text-warn">不开启隐私面单可能导致消费者隐私泄露，引起客诉。</span>
            开通后，我们将在所有订单的快递包裹运输的全链路中隐藏收件人的真实手机号码，以此来保护收件人的隐私安全，为消费者保驾护航。
            <PopContainer
              popContent={
                <div className="privacy-example">
                  <img
                    src="https://img01.yzcdn.cn/upload_files/2023/09/11/FoxzlVhTt3XYwjEeOLMTL3lqtfJ6.png"
                    alt="示例"
                  />
                </div>
              }
              content={<a className="text-pointer">查看示例</a>}
              position={position}
            />
          </li>
          <li>
            预计服务费：最低￥0.06/笔
            <PopContainer
              popContent={
                <div>
                  <ChargeStandard />
                </div>
              }
              content={<a className="text-pointer">查看收费标准</a>}
              position={position}
            />
          </li>
        </ul>
      }
    >
      同意协议并开启服务
    </FormCheckboxField>
  );
};

export default PrivacyFormField;

/**
 * 由于retail-node-order的zent版本过低，导致与pc-trade的zent10版本的react-components组件无法兼容，所以无法使用zent的BlankLink
 */
import * as React from 'react';
import { filterXSS } from 'xss';
import { mapValues } from 'lodash-es';

type PartialRequired<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

export interface IRichTextPreviewProps {
  tag?: keyof React.ReactHTML;
  content: string;
}

const commonAttrs = ['style'];

const baseWhiteList = {
  a: ['target', 'href', 'title'],
  abbr: ['title'],
  address: [],
  area: ['shape', 'coords', 'href', 'alt'],
  article: [],
  aside: [],
  audio: ['autoplay', 'controls', 'loop', 'preload', 'src'],
  b: [],
  bdi: ['dir'],
  bdo: ['dir'],
  big: [],
  blockquote: ['cite'],
  br: [],
  caption: [],
  center: [],
  cite: [],
  code: [],
  col: ['align', 'valign', 'span', 'width'],
  colgroup: ['align', 'valign', 'span', 'width'],
  dd: [],
  del: ['datetime'],
  details: ['open'],
  div: [],
  dl: [],
  dt: [],
  em: [],
  font: ['color', 'size', 'face'],
  footer: [],
  h1: [],
  h2: [],
  h3: [],
  h4: [],
  h5: [],
  h6: [],
  header: [],
  hr: [],
  i: [],
  img: ['src', 'alt', 'title', 'width', 'height'],
  ins: ['datetime'],
  li: [],
  mark: [],
  nav: [],
  ol: [],
  p: [],
  pre: [],
  s: [],
  section: [],
  small: [],
  span: [],
  sub: [],
  sup: [],
  strong: [],
  table: ['width', 'border', 'align', 'valign'],
  tbody: ['align', 'valign'],
  td: ['width', 'rowspan', 'colspan', 'align', 'valign'],
  tfoot: ['align', 'valign'],
  th: ['width', 'rowspan', 'colspan', 'align', 'valign'],
  thead: ['align', 'valign'],
  tr: ['rowspan', 'align', 'valign'],
  tt: [],
  u: [],
  ul: [],
  video: ['autoplay', 'controls', 'loop', 'preload', 'src', 'height', 'width']
};

const whiteList = mapValues(baseWhiteList, (attrs) => [...attrs, ...commonAttrs]);

/**
 * 富文本统一展示组件
 */
const RichTextPreview: React.FC<IRichTextPreviewProps> = React.memo((props): any => {
  const { tag: Tag, content } = props as PartialRequired<IRichTextPreviewProps, 'tag'>;

  const html = filterXSS(content, {
    css: true,
    whiteList
  });

  return <Tag dangerouslySetInnerHTML={{ __html: html }} />;
});

RichTextPreview.defaultProps = {
  tag: 'div'
};

export default RichTextPreview;

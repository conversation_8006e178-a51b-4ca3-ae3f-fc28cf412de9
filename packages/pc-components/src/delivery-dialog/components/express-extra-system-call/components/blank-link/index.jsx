/**
 * 由于retail-node-order的zent版本过低，导致与pc-trade的zent10版本的react-components组件无法兼容，所以无法使用zent的BlankLink
 */

import React from 'react';
import cn from 'classnames';

// 定义防止打开链接的函数
function preventOpenLink(event) {
  event.preventDefault();
  event.stopPropagation();
}

// 定义 BlankLink 组件
function BlankLink(props) {
  const { autoRel = true, className, href, children, disabled, ...restProps } = props;
  let disabledProp = {};
  if (disabled) {
    disabledProp = { onClick: preventOpenLink };
  }
  let relProp = {};
  try {
    // eslint-disable-next-line compat/compat
    const url = new URL(href, window.location.href);
    const localHost = window.location.host;
    const targetHost = url.host;
    relProp = autoRel && localHost !== targetHost ? { rel: 'noopener noreferrer' } : {};
  } catch (error) {
    console.error(error);
  }
  return (
    // eslint-disable-next-line react/jsx-no-target-blank
    <a
      target="_blank"
      href={href}
      className={cn('rc-blank-link', { 'rc-blank-link__disabled': disabled }, className)}
      {...relProp}
      {...restProps}
      {...disabledProp}
    >
      {children}
    </a>
  );
}

BlankLink.defaultProps = {
  autoRel: true
};

export default BlankLink;

import React from 'react';
import { Pop, Icon, IconType } from 'zent';

interface IPopContainerProps {
  icon?: IconType;
  iconStyle?: any;
  content?: any;
  popContent: any;
  [t: string]: any;
}
const PopContainer = (props: IPopContainerProps) => {
  const { icon, iconStyle, content, popContent, ...rest } = props;
  return (
    <Pop className="12" content={popContent} trigger="hover" position="top-center" {...rest}>
      <span>
        {content}
        {icon ? <Icon type={icon} style={iconStyle} /> : null}
      </span>
    </Pop>
  );
};

export default PopContainer;

import { get } from 'lodash';
import { WaybillVersionEnum } from '../types';

// 周期购上一期时间未到错误码
export const PERIOD_EXPRESS_ERR_CODE = *********;
// 制作店铺店铺发货需先改派 错误码
export const PRODUCE_SHOP_SEND_ERR_CODE = *********;
// 系统呼叫快递，电子面单
export const EXPRESS_SYSTEM_CALL = 14;

// 物流公司发货
export const EXPRESS_FOR_COMPANY = 12;

// 商家物流自配送
export const EXPRESS_SELF = 12;

// 无需物流(同城配送)
export const SELLER_NO_EXPRESS = 23;

// 无需物流 (快递)
export const NO_EXPRESS = 13;

// 微信物流 (快递)
export const WX_EXPRESS = 15;

// 同城配送第三方发货
export const THIRD_LOCAL_DELIVERY = 21;

// 同城配送商家自行配送
export const SELF_LOCAL_DELIVERY = 22;

// 自提有码核销
export const SELFFETCH_WITH_CODE = 91;

// 自提无码核销
export const SELFFETCH_WITHOUT_CODE = 92;

// 仅电子面单
export const ONLY_DZMD = 1;

// 上门取件加电子面单
export const SMQJ_AND_DZMD = 2;

export const SYSTEM_CALL_TYPE_MAP = {
  [ONLY_DZMD]: '仅打印面单',
  [SMQJ_AND_DZMD]: '上门取件加电子面单'
};

// 京东物流
export const JD_EXPRESS_CODE = 138;

// 顺丰
export const SF_EXPRESS_CODE = 7;

// 申通
export const ST_EXPRESS_CODE = 1;

export const SEND_TYPE_CODE = {
  EXPRESS_SYSTEM_CALL,
  EXPRESS_FOR_COMPANY,
  THIRD_LOCAL_DELIVERY,
  SELF_LOCAL_DELIVERY,
  SELFFETCH_WITH_CODE,
  SELFFETCH_WITHOUT_CODE,
  WX_EXPRESS
};

// 验证信息
export const validateInfo = {
  [EXPRESS_SYSTEM_CALL]: [
    {
      path: 'express.expressId',
      errDesc: '物流公司未选择!'
    },
    {
      path: 'express.auditNo',
      errDesc: '发货地址未选择!',
      waybillVersion: WaybillVersionEnum.Old
    },
    {
      path: 'express.auditNo',
      subPath: 'express.fakeId',
      errDesc: '发货地址未选择!',
      waybillVersion: WaybillVersionEnum.New
    },
    {
      path: 'express.printerDeviceNo',
      errDesc: '打印机未选择!',
      waybillVersion: WaybillVersionEnum.Old
    },
    {
      path: 'express',
      waybillVersion: WaybillVersionEnum.Old,
      validate: ({
        expressId,
        eWaybillTemplateId
      }: {
        expressId: number;
        eWaybillTemplateId: number;
      }) => {
        if (expressId !== JD_EXPRESS_CODE) {
          return true;
        }
        return Boolean(eWaybillTemplateId);
      },
      errDesc: '请选择面单模板!'
    },
    {
      // 取件时间
      path: 'express',
      waybillVersion: WaybillVersionEnum.Old,
      validate: ({ exprssId, startTime }: { exprssId: number; startTime: string }) =>
        !(exprssId === SF_EXPRESS_CODE && !startTime),
      errDesc: '请选择上门取件时间!'
    }
  ],
  [EXPRESS_FOR_COMPANY]: [
    {
      path: 'express.expressId',
      errDesc: '物流公司未选择!'
    },
    {
      path: 'express.expressNo',
      errDesc: '物流单号未输入!'
    }
  ],
  [THIRD_LOCAL_DELIVERY]: [
    {
      path: 'localDelivery.channel',
      errDesc: '请选择第三方配送公司！'
    }
  ],
  [SELFFETCH_WITH_CODE]: [
    {
      path: 'selfFetch.selfFetchNo',
      errDesc: '请输入核销码！'
    }
  ],
  [WX_EXPRESS]: [
    {
      path: 'express.expressId',
      errDesc: '物流公司未选择!'
    },
    {
      path: 'express.accountNo',
      errDesc: '发货地址未选择!'
    }
  ]
};

const NOT_SUBSCRIBE_ERROR = *********; // "店铺暂未订购电子面单"
export const ELECTRON_WAYBILL_SERVICE_CLOSED = *********; // "电子面单服务已关闭"
const ELECTRON_WAYBILL_SERVICE_EXPIRED = *********; // "店铺电子面单服务已到期"

export const NOT_SUFFICIENT_FUNDS_CODE = *********; // 计算上门取件运费时余额不足的异常码

const NO_DREDGE_APPROVE = *********; // 未开通个人/企业认证
export const NO_ADD_DEPOSIT_SERVICE = *********; // 未加入保证金服务
const DEPOSIT_BALANCE_NOT_ENOUGH = *********; // 保证金余额不足
const DEPOSIT_QUIT_DEALING = *********; // 退保中

const storeUrl = get(window, '_global.url.store', '');
// 电子面单错误异常的链接信息
export const ELECTRON_ERROR_INFO = {
  [NOT_SUBSCRIBE_ERROR]: {
    link: `${storeUrl}/v4/ump/electronWayBill#/`,
    text: '去订购'
  },
  [ELECTRON_WAYBILL_SERVICE_CLOSED]: {
    link: `${storeUrl}/v4/ump/electronWayBill#/`,
    text: '去开启'
  },
  [ELECTRON_WAYBILL_SERVICE_EXPIRED]: {
    link: `${storeUrl}/shop/v2/appmarket/appdesc?id=16401`,
    text: '去续订'
  },

  [NOT_SUFFICIENT_FUNDS_CODE]: {
    link: `${storeUrl}/shop/v2/trade/newsettlement#/rechargeMoney`,
    text: '去充值'
  },

  [NO_DREDGE_APPROVE]: {
    link: `${storeUrl}/shop/v2/account/cert#/editor`,
    text: '去认证'
  },
  [NO_ADD_DEPOSIT_SERVICE]: {
    link: `${storeUrl}/v4/ump/logisticsService#/bond`,
    text: '去加入'
  },
  [DEPOSIT_BALANCE_NOT_ENOUGH]: {
    link: `${storeUrl}/v4/ump/logisticsService#/bond`,
    text: '去充值'
  },
  [DEPOSIT_QUIT_DEALING]: {
    link: `${storeUrl}/v4/ump/logisticsService#/bond`,
    text: '去查看',
    intercept: true
  }
};

export const WARE_HOUSE_TYPE = {
  ALONE_WARE_HOUSE: 1,
  STORE_WARE_HOUSE: 2
};

export const WX_ORDER_MARK = 'wx_shop';

export const CalcWeightType = {
  /** 默认 5千克以内 */
  Default: 'default',
  /** 自定义 */
  Custom: 'custom'
};

/** 50 Kg */
export const MaxGoodsWeight = 50000;

/** 保存在本地的服务商 */
// eslint-disable-next-line @youzan/yz-retail/prefer-pascal-case-const
export const EXPRESS_CHANNEL_KEY_NAME = 'retail.order.localdelivery.express.channel';

export const EXPRESS_WAY_BILL_TYPES_MAP = {
  1: ['printOnly'],
  2: ['printAndCallCourier'],
  4: ['callCourierAndPrint']
};

export const EXPRESS_WAY_BILL_TYPES = {
  printAndCallCourier: {
    value: 2,
    text: '打印面单并通知快递员揽件'
  },
  callCourierOnly: {
    text: '仅呼叫快递员上门取件',
    type: 3
  },
  printOnly: {
    value: 1,
    text: '仅打印面单'
  },
  callCourierAndPrint: {
    value: 4,
    text: '快递员上门打印面单'
  }
};

export const EXPRESS_SUPPORT_DETAIL_PICK_TIME = [
  7 // 顺丰
];

// 电子面单表单类型：普通/批量打单发货
export enum ExpressWayBillFormType {
  Normal,
  BatchDeliveryPrint
}
// 获取运费描述
export const getFeeDescMap = {
  [ExpressWayBillFormType.Normal]: () =>
    '预估费用仅供参考，快递公司称重后，有赞将按照实际计费重量扣费',
  [ExpressWayBillFormType.BatchDeliveryPrint]: () =>
    '包裹按重量1kg预估运费，快递公司称重后，有赞将按照实际计费重量扣费'
};

// 资产接口要求传source
export const OPEN_EXPRESS_PAGE_SOURCE = 'orderExpressDialog';

// 微商城电子面单信息映射关系 - [门店key]: [微商城key]
export const expressFieldMapping = {
  sendType: 'expressWayBillType',
  eWaybillTemplateId: 'templateUrl',
  printerDeviceNo: 'printerId',
  expressWayBillAddress: 'address'
};

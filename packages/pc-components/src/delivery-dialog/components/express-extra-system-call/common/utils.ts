import { isRetailShop } from '@youzan/utils-shop';
import addDays from 'date-fns/add_days';
import getYear from 'date-fns/get_year';
import getMonth from 'date-fns/get_month';
import getDate from 'date-fns/get_date';
import setHours from 'date-fns/set_hours';
import getTime from 'date-fns/get_time';
import { EXPRESS_WAY_BILL_TYPES, SF_EXPRESS_CODE, expressFieldMapping } from './constants';
import {
  setExpressWayBillStorage,
  EXPRESS_LOCAL_STORAGE_KEY,
  setWayBillTypeStorage,
  setExpressPrinter
} from './storage';

function formatExpressData(express, options) {
  const { pickTime, logisticsServicesView, ...rest } = express;
  const { expressId, expressWayBillType } = rest;
  rest.waybillVersion = options.waybillVersion;
  if (
    expressId === SF_EXPRESS_CODE &&
    (expressWayBillType === EXPRESS_WAY_BILL_TYPES.printAndCallCourier.value ||
      expressWayBillType === EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value)
  ) {
    const now = new Date();
    // 后端需要时间精确到秒
    const startAppointment =
      getTime(
        addDays(
          // eslint-disable-next-line @youzan/yz-retail/no-new-date
          setHours(new Date(getYear(now), getMonth(now), getDate(now)), pickTime.time),
          pickTime.day as number
        )
      ) / 1000;
    rest.startAppointment = startAppointment;
    rest.endAppointment = startAppointment + 3600;
  }
  // 增值服务数据格式处理
  if (logisticsServicesView) {
    rest.logisticsServices = JSON.stringify(logisticsServicesView);
  }
  const deleteKeys = ['expressAddrList', 'receiveTimeRemark'];
  deleteKeys.forEach((key) => {
    delete rest[key];
  });
  return rest;
}

function formatExpressForRetail(express, options) {
  const result = { ...express };
  result.waybillVersion = options.waybillVersion;
  result.providerKdtId = options.currentElectronWayBillServiceKdtId;
  return result;
}

function formatExpressForWsc(express) {
  const { weight, ...result } = express;
  const retailFieldKeys = Object.keys(expressFieldMapping);
  retailFieldKeys.forEach((key) => {
    delete result[key];
  });
  if (weight) {
    result.weight = weight;
  }
  return result;
}

/**
 * 数据格式化
 * @param {*} express
 */
export function clientDataToServerData(express, options = {}) {
  const nextExpress = formatExpressData(express, options);
  if (isRetailShop) {
    return formatExpressForRetail(nextExpress, options);
  }
  return formatExpressForWsc(nextExpress);
}

// 缓存数据
export const handleSetDefaultExpressLocalStorage = (
  express,
  { currentElectronWayBillServiceKdtId }
) => {
  const {
    expressId,
    paymentType,
    sendType,
    templateUrl = '',
    auditNo,
    fakeId,
    productCode = '',
    brandCode = '',
    printerDeviceNo
  } = express;
  if (expressId) {
    window.localStorage.setItem(
      `${EXPRESS_LOCAL_STORAGE_KEY}_${currentElectronWayBillServiceKdtId}`,
      `${expressId}_${paymentType}`
    );
  }
  setExpressWayBillStorage(currentElectronWayBillServiceKdtId, expressId, paymentType, {
    eWaybillTemplateId: templateUrl,
    auditNo,
    fakeId,
    productCode,
    brandCode
  });
  setWayBillTypeStorage(currentElectronWayBillServiceKdtId, sendType);
  setExpressPrinter(currentElectronWayBillServiceKdtId, String(printerDeviceNo));
};

const checkDeliveryAddress = (express) => {
  const currentAddress = express.expressAddrList?.find(
    (res) => res.auditNo === (express.auditNo || express.fakeId)
  );
  const hasAbility = currentAddress?.hasAbility;
  if (hasAbility === false && currentAddress?.reason) {
    return false;
  }
  return true;
};

/**
 * 电子面单是否有效
 * 1. 检查地址是否不可用
 * 2. 检查有赞寄件的场景保证金是否可用
 * @param express 电子面单
 * @returns {boolean}
 */
export const checkExpressValid = (express) => {
  let isValid = checkDeliveryAddress(express);
  if (express?.isPay) {
    isValid = isValid && express.depositOffline;
  }
  return isValid;
};

import retailAjax from '@youzan/retail-ajax';
import { request } from '@youzan/retail-utils';

// 获取新电子面单服务商
export function getExpressByNewWayBill(data) {
  return request({
    url: '/v4/trade/electron-way-bill/available-express-list.json',
    data
  });
}

// 获取有赞寄件开通状态
export function getYZShoppingServiceInfo(data) {
  return retailAjax({
    url: '/v4/assets/api/shipping/getServiceInfo',
    method: 'GET',
    data
  });
}

export function getDeliveryAddresses(data) {
  return retailAjax({
    url: '/v4/trade/delivery/address.json',
    method: 'GET',
    data
  });
}

// 获取有赞寄件发货地址
export function getAuthorityDeliveryAddresses(data) {
  return retailAjax({
    url: '/v4/trade/delivery/address-authority.json',
    method: 'GET',
    data
  });
}

// 获取新电子面单增值服务
export function getAppendServiceList(data) {
  return retailAjax({
    url: '/v4/trade/electron-way-bill/append-service-list.json',
    method: 'GET',
    data
  });
}

// 获取新电子面单产品类型
export function getExpressProductTypes(data) {
  return retailAjax({
    url: '/v4/trade/electron-way-bill/express-product-type.json',
    method: 'GET',
    data
  });
}

// 校验快递地址可达性
export function getExpressAbility(data) {
  return retailAjax({
    url: '/v4/trade/electron-way-bill/express-ability.json',
    method: 'GET',
    data
  });
}

// 获取新电子面单打印模板
export function getWayBillTemplate(data) {
  return retailAjax({
    url: '/v4/trade/electron-way-bill/way-bill-template.json',
    method: 'GET',
    data
  });
}

// 获取新电子面单快递品牌
export function getExpressBrands(data) {
  return retailAjax({
    url: '/v4/trade/electron-way-bill/express-brand.json',
    method: 'GET',
    data
  });
}

export function getDeliveryFee(data) {
  return request({
    url: '/v4/trade/delivery/fee.json',
    data
  });
}

export function getDeliveryBatchFee(data) {
  return request({
    url: '/v4/trade/delivery/fee-batch.json',
    data
  });
}

// 检测地址是否在可免打单城市内
export function queryDeliveryBizTypeByExpressAndAddress(data) {
  return request({
    method: 'GET',
    url: '/v4/trade/api/express/queryDeliveryBizTypeByExpressAndAddress',
    data
  });
}

// 批量发货运费计算
export function getBatchDeliveryFee(data) {
  const url = '/v4/trade/delivery/batch-delivery-fee.json';
  return retailAjax({
    url,
    method: 'POST',
    data
  });
}

// 批量发货运费计算（零售）
export function getBatchDeliveryFeeByRetail(data) {
  const { orderNos, expressId, sendProvince, sendCity, retailKdtId } = data;
  const postData = {
    orderNos,
    weight: 1000,
    expressIds: [+expressId],
    sendProvince,
    sendCity,
    kdtId: retailKdtId
  };
  const url = '/youzan.logistics.delivery.query.batchfee/1.0.0';
  return retailAjax({
    url,
    method: 'POST',
    data: postData
  }).then((data) => {
    const deliveryBatchCalculateFeeList = data.deliveryBatchCalculateFeeList || [];
    const totalPrice = deliveryBatchCalculateFeeList.reduce((prev, item) => {
      return prev + item.realPrice;
    }, 0);
    const result = {
      deliveryBatchCalculateFeeList,
      totalPrice
    };
    return result;
  });
}

// 申请开通隐私面单
export const applyOpenPrivacy = (data) => {
  const url = `/v4/assets/api/security/privacy-waybill/apply`;
  return retailAjax({
    url,
    method: 'POST',
    data
  });
};

// 申请开通有赞寄件
export const applyOpenYzAuthority = (data) => {
  const url = '/v4/assets/api/shipping/apply';
  return retailAjax({
    url,
    method: 'POST',
    data
  });
};

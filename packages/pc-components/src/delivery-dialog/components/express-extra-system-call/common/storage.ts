import YZlocalstorage from '@youzan/utils/browser/local_storage';

/** 保存上次选择的快递公司 */
export const EXPRESS_LOCAL_STORAGE_KEY = 'express-way-bill-cache-express-key';

/**
 * 在线下单缓存数据，快递公司维度
 */
const EXPRESS_WAY_BILL_SELECTED = 'EXPRESS_WAY_BILL_SELECTED_ITEM';
export const getExpressWayBillStorage = (
  currentElectronWayBillServiceKdtId: number,
  expressId?: number,
  paymentType?: number
): Record<string, any> => {
  const storageKey = [expressId, paymentType].join('_');
  const data = YZlocalstorage.getItem(
    `${EXPRESS_WAY_BILL_SELECTED}_${currentElectronWayBillServiceKdtId}`
  );
  try {
    const parseData = JSON.parse(data);
    return (expressId ? parseData[storageKey] : parseData) || {};
  } catch (err) {
    return {};
  }
};

export const setExpressWayBillStorage = (
  currentElectronWayBillServiceKdtId: number,
  expressId: number,
  paymentType: number,
  opt: Record<string, any>
): void => {
  const storageKey = [expressId, paymentType].join('_');
  const storage = getExpressWayBillStorage(currentElectronWayBillServiceKdtId);
  const nextStorage = {
    ...storage,
    [storageKey]: {
      ...storage[storageKey],
      ...opt
    }
  };

  YZlocalstorage.setItem(
    `${EXPRESS_WAY_BILL_SELECTED}_${currentElectronWayBillServiceKdtId}`,
    JSON.stringify(nextStorage)
  );
};

/**
 * 发货类型缓存
 */
const WAYBILL_TYPE_SELECTED = 'WAYBILL_TYPE_SELECTED';

export const setWayBillTypeStorage = (
  currentElectronWayBillServiceKdtId: number,
  val: number
): void => {
  YZlocalstorage.setItem(`${WAYBILL_TYPE_SELECTED}_${currentElectronWayBillServiceKdtId}`, val);
};

export const getWayBillTypeStorage = (
  currentElectronWayBillServiceKdtId: number
): number | null => {
  const data = YZlocalstorage.getItem(
    `${WAYBILL_TYPE_SELECTED}_${currentElectronWayBillServiceKdtId}`
  );
  return data ? +data : null;
};

// 打印机缓存，记住上一次的打印机
export const EXPRESS_SEND_PRINTER = 'EXPRESS_SEND_PRINTER';

export const setExpressPrinter = (
  currentElectronWayBillServiceKdtId: number,
  val: string
): void => {
  YZlocalstorage.setItem(`${EXPRESS_SEND_PRINTER}_${currentElectronWayBillServiceKdtId}`, val);
};

export const getExpressPrinter = (currentElectronWayBillServiceKdtId: number): string | null => {
  const data = YZlocalstorage.getItem(
    `${EXPRESS_SEND_PRINTER}_${currentElectronWayBillServiceKdtId}`
  );
  return data || null;
};

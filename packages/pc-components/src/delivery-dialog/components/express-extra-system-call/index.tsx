import React, { useEffect } from 'react';
import { isRetailShop } from '@youzan/utils-shop';

import ExpressExtraSystemCallRetail from './retail';
import ExpressExtraSystemCallWsc from './wsc';
import { ExpressWayBillFormType } from './common/constants';

import {
  getExpressPostData,
  setDefaultExpressLocalStorage,
  checkExpressValid,
  renderFooterExtra,
  beforeConfirmShipment,
} from './extra';

interface IHelper {
  getExpressPostData: (e: any) => any;
  setExpressStorage: (e: any) => any;
  checkExpressValid: (e: any) => any;
  renderFooterExtra: (e: any) => React.ReactNode;
  beforeConfirmShipment: (e: any) => Promise<void>;
}

const { kdtId, shopName } = window._global;

interface IProps {
  express: any; // 电子面单配置
  waybillVersion: number; // 电子面单版本号
  orderNo?: string;
  orderNos?: string[]; // 订单号列表 - 批量打单发货传入
  isEditExpress?: boolean; // 是否修改电子面单
  formType?: ExpressWayBillFormType; // 电子面单表单类型 - 批量打单发货会传值
  onExpressValueChange: (value: any, isCover: boolean) => void; // 电子面单值变更
  generateHelper?: (helper: IHelper) => void; // 电子面单提供的工具方法

  /* 下面是门店需要的数据 */
  currentElectronWayBillServiceKdtId?: number; // 门店电子面单服务的kdtId
  currentElectronWayBillServiceKdtName?: string; // 门店电子面单服务的kdtName
  electronicSheetExceptionDesc?: string; // 门店电子面单异常信息
  electronicSheetExceptionCode?: string; // 门店电子面单异常码
  electronWayBillServiceKdtIdMap?: any; // 门店电子面单服务kdtId映射表
}

const ExpressExtraSystemCall = (props: IProps) => {
  const { express, currentElectronWayBillServiceKdtId, waybillVersion, generateHelper } = props;

  useEffect(() => {
    if (!generateHelper) {
      return;
    }
    const extOptions = {
      kdtId: currentElectronWayBillServiceKdtId || kdtId,
      waybillVersion
    };
    // 使用电子面单的时候,传入generateHelper，将会返回一个电子面单的工具对象
    // 所有父级需要依赖电子面单相关能力都放到这里
    generateHelper({
      getExpressPostData: (currentExpress) => {
        return getExpressPostData(currentExpress || express, extOptions);
      },
      setExpressStorage: (currentExpress) => {
        return setDefaultExpressLocalStorage(currentExpress || express, extOptions);
      },
      checkExpressValid: (currentExpress) => {
        return checkExpressValid(currentExpress || express);
      },
      renderFooterExtra: (currentExpress) => {
        return renderFooterExtra(currentExpress || express);
      },
      beforeConfirmShipment: async (currentExpress) => {
        return beforeConfirmShipment(currentExpress || express);
      }
    });
    // 纯函数使用此组件，generateHelper记得加useCallback，否则导致无限渲染。
  }, [generateHelper, express, currentElectronWayBillServiceKdtId, waybillVersion]);

  if (isRetailShop) {
    return <ExpressExtraSystemCallRetail {...props} />;
  }
  return (
    <ExpressExtraSystemCallWsc
      currentElectronWayBillServiceKdtId={kdtId}
      currentElectronWayBillServiceKdtName={shopName}
      {...props}
    />
  );
};

export default ExpressExtraSystemCall;

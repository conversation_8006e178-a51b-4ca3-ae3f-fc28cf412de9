import React, { useEffect, useMemo } from 'react';

import { ExpressExtraSystemCallBase } from './base';
import { getYZShoppingServiceInfo } from './common/api';
import { getWayBillTypeStorage } from '../../storage';
import {
  SF_EXPRESS_CODE,
  JD_EXPRESS_CODE,
  EXPRESS_WAY_BILL_TYPES,
  expressFieldMapping
} from './common/constants';
import { PaymentTypeEnum, YZShippingStatusEnum } from './types';

const ExpressExtraSystemCallWsc = (props) => {
  const { express, currentElectronWayBillServiceKdtId } = props;

  const getSendType = (allType, selectedExpress) => {
    const storeWayBillType = getWayBillTypeStorage(currentElectronWayBillServiceKdtId);
    // 初始化 expressWayBillType 变量
    let expressWayBillType;

    // 只有一个选项时，直接使用
    if (allType.length === 1) {
      [expressWayBillType] = allType;
      return expressWayBillType;
    }

    // 非官方结算模式，使用默认类型
    if (selectedExpress.paymentType !== PaymentTypeEnum.Authority) {
      expressWayBillType = selectedExpress.type;
      return expressWayBillType;
    }

    // 官方结算模式下的特殊处理
    const { expressId } = selectedExpress;

    // 优先使用缓存的面单类型（如果在可选范围内）
    if (allType.includes(storeWayBillType)) {
      expressWayBillType = storeWayBillType;
      return expressWayBillType;
    }

    // 顺丰快递的特殊逻辑：优先选择"快递员上门打印面单"
    if (
      expressId === SF_EXPRESS_CODE &&
      allType.includes(EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value)
    ) {
      expressWayBillType = EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value;
      return expressWayBillType;
    }
    // 京东快递的特殊逻辑：不展示面单类型选择
    if (expressId === JD_EXPRESS_CODE) {
      return null;
    }

    // 其他情况，使用默认类型
    return selectedExpress.type;
  };

  const handleExpressValueChange = (value, isCover = false) => {
    // 字段映射
    Object.keys(value).forEach((key) => {
      if (expressFieldMapping[key]) {
        value[expressFieldMapping[key]] = value[key];
      }
    });
    props.onExpressValueChange(value, isCover);
  };

  function fetchYZShoppingInfo() {
    getYZShoppingServiceInfo().then(({ serviceStatus, overdueFee, overdueLimitFee }) => {
      handleExpressValueChange({
        YZShoppingInfo: {
          waitJoin: serviceStatus === YZShippingStatusEnum.WAIT_JOIN,
          joined: serviceStatus === YZShippingStatusEnum.JOINED,
          suspended: serviceStatus === YZShippingStatusEnum.SUSPEND,
          overdueFee,
          overdueLimitFee
        }
      });
    });
  }

  useEffect(() => {
    fetchYZShoppingInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 字段映射
  const expressFormat = useMemo(() => {
    Object.keys(expressFieldMapping).forEach((key) => {
      const serviceKey = expressFieldMapping[key];
      express[key] = express[serviceKey];
    });
    return express;
  }, [express]);

  return (
    <div>
      <ExpressExtraSystemCallBase
        {...props}
        express={expressFormat}
        showPrivacy
        onExpressValueChange={handleExpressValueChange}
        getSendType={getSendType}
      />
    </div>
  );
};

export default ExpressExtraSystemCallWsc;

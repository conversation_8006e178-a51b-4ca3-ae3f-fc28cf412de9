export interface IAppendServiceAttributeVo {
  attributeCode: string; // 属性code
  typeDesc: string; // number的结构 {"max":300000,"min":0,"required":true,"type":"number"} string的结构 {"maxLength":128,"minLength":1,"required":true,"type":"string"} enum的结构 {"desc":{"NORMAL":"通用","OTHER":"其他"},"required":true,"type":"enum"}
  typeDescModel: any; // typeDesc的对象模型，额外增加了date类型
  attributeType: string; // 属性类型  number表示数值，string表示字符，enumeration标识枚举
  attributeName: string; // 属性名称
}

// 增值服务对象vo
export interface IAppendServiceVo {
  /** 网点编码 */
  latticePointNo?: string;
  /** 服务描述 */
  serviceDesc: string;
  /** 是否可订购 */
  canSubscript?: boolean;
  /** 服务编码 */
  serviceCode: string;
  /** 服务名称 */
  serviceName: string;
  /** 订购状态  0:未开通,1:审核中,2:审核未通过,3:已开通 */
  subscriptionStatus: number;
  /** 服务id */
  thirdServiceId: number;
  /** 快递公司ID  必填 */
  expressId: number;
  /** 审核不通过原因 */
  failReason?: string;
  /** 是否必填 */
  isRequire: boolean;
  /** 是否需要属性 */
  needAttribute: boolean;
  /** 附加属性 */
  valueAddedAttributeModels?: IAppendServiceAttributeVo[];
}

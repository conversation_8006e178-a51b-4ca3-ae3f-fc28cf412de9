import React, { useEffect } from 'react';

import BlankLink from './components/blank-link';
import { ExpressExtraSystemCallBase } from './base';
import { getWayBillTypeStorage } from '../../storage';
import { getYZShoppingServiceInfo } from './common/api';
import { YZShippingStatusEnum } from './types';
import { ELECTRON_ERROR_INFO, ELECTRON_WAYBILL_SERVICE_CLOSED } from './common/constants';


const ExpressExtraSystemCallRetail = (props) => {
  const {
    currentElectronWayBillServiceKdtId,
    electronWayBillServiceKdtIdMap,
    electronicSheetExceptionDesc,
    electronicSheetExceptionCode,
    onExpressValueChange
  } = props;

  const fetchYZShoppingInfo = () => {
    const payKdtId = electronWayBillServiceKdtIdMap?.payKdtId;
    const fns = [getYZShoppingServiceInfo({ retailKdtId: currentElectronWayBillServiceKdtId })];

    const isSameKdtId = payKdtId === currentElectronWayBillServiceKdtId;

    if (!isSameKdtId) {
      fns.push(getYZShoppingServiceInfo({ retailKdtId: payKdtId }));
    }

    Promise.all(fns).then(([serviceInfo, payServiceInfo]) => {
      if (isSameKdtId) {
        // eslint-disable-next-line no-param-reassign
        payServiceInfo = serviceInfo;
      }
      const info = {
        waitJoin: serviceInfo.serviceStatus === YZShippingStatusEnum.WAIT_JOIN,
        joined: serviceInfo.serviceStatus === YZShippingStatusEnum.JOINED,
        suspended: payServiceInfo.serviceStatus === YZShippingStatusEnum.SUSPEND,
        overdueFee: payServiceInfo.overdueFee,
        overdueLimitFee: payServiceInfo.overdueLimitFee
      };
      onExpressValueChange({
        YZShoppingInfo: info
      });
    });
  };

  useEffect(() => {
    if (currentElectronWayBillServiceKdtId) {
      fetchYZShoppingInfo();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentElectronWayBillServiceKdtId]);

  // 获取发货类型
  const getSendType = (allType, selectedExpress) => {
    let sendType;
    const wayBillTypeStorage = getWayBillTypeStorage(currentElectronWayBillServiceKdtId);
    if (allType.length === 1) {
      [sendType] = allType;
    } else if (allType.includes(wayBillTypeStorage)) {
      sendType = wayBillTypeStorage;
    } else if (selectedExpress.sendType) {
      sendType = selectedExpress.sendType;
    } else if (allType.includes(4) /* 默认的最后兜底是 快递员上门打印电子面单 */) {
      sendType = 4 /* 快递员上门打印面单 */;
    } else if (!sendType && allType.length > 0) {
      // 这个是最后的兜底，allType中4不是所有都有的，比如自结算的顺丰速运
      [sendType] = allType;
    }

    return sendType;
  };

  // 在线下单，发货弹窗直接抛出的异常，
  if (
    electronicSheetExceptionDesc &&
    electronicSheetExceptionCode !== ELECTRON_WAYBILL_SERVICE_CLOSED
  ) {
    const linkInfo = ELECTRON_ERROR_INFO[electronicSheetExceptionCode];
    return (
      <p className="orange pl100">
        {electronicSheetExceptionDesc}
        {linkInfo && <BlankLink href={linkInfo.link}>{linkInfo.text}</BlankLink>}
      </p>
    );
  }

  return (
    <div>
      <ExpressExtraSystemCallBase {...props} getSendType={getSendType} />
    </div>
  );
};

export default ExpressExtraSystemCallRetail;

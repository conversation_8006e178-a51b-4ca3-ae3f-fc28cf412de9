import { setUrlDomain, plus, minus, times, div } from '@youzan/retail-utils';
import formatMoney from '@youzan/utils/money/format';
import { Select, Form } from '@zent/compat';
import { find, isNumber } from 'lodash';
import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Notify, Radio, Checkbox, Tooltip } from 'zent';
import cx from 'classnames';
import { isRetailShop } from '@youzan/utils-shop';
import { PaymentTypeEnum, ExpressWayBillFormType } from './types';
import * as api from './common/api';
import {
  ELECTRON_ERROR_INFO,
  JD_EXPRESS_CODE,
  ST_EXPRESS_CODE,
  SF_EXPRESS_CODE,
  ELECTRON_WAYBILL_SERVICE_CLOSED,
  EXPRESS_WAY_BILL_TYPES_MAP,
  EXPRESS_WAY_BILL_TYPES,
  EXPRESS_SUPPORT_DETAIL_PICK_TIME
} from '../../constants';
import {
  getExpressWayBillStorage,
  EXPRESS_LOCAL_STORAGE_KEY,
  getExpressPrinter,
  setExpressPrinter
} from '../../storage';
import { prefixZero } from '../../utils';
import { defaultExpressData } from '../../default';
import BlankLink from './components/blank-link';
import RichTextPreview from './components/rich-text-preview';
import AppendServiceSelection from './append-service-selection';
import { getFeeDescMap, OPEN_EXPRESS_PAGE_SOURCE } from './common/constants';
import PrivacyFormField from './components/privacy-form-field';
import { getPrinters } from '../../../cainiao-printer/printer';
import PopContainer from './components/pop-container';
import { getExpressCompaniesCache, setExpressCompaniesCache } from './common/cache';

// 类型定义
interface ExpressCompany {
  expressId: number;
  expressName: string;
  paymentType: number;
  channelStatus?: number;
  recommendPriority?: number;
  brandCode?: string;
  isPay?: boolean;
  recommend?: boolean;
  freightStandardUrl?: string;
  allType?: number[];
  logisticsRecommendInfo?: {
    originPrice?: number;
    estimateAmount?: number;
  };
}

interface ExpressAddress {
  auditNo: string;
  id?: string;
  provinceName: string;
  cityName: string;
  countyName: string;
  address: string;
  contactName?: string;
  mobile?: string;
  phone?: string;
  hasAbility?: boolean;
  reason?: string;
  brandCode?: string;
  latticePointNo?: string;
}

interface ExpressInfo {
  expressId?: number;
  expressName?: string;
  paymentType?: number;
  sendType?: number;
  auditNo?: string;
  fakeId?: string;
  eWaybillTemplateId?: string;
  productCode?: string;
  weight?: number;
  printerDeviceNo?: string;
  pickTime?: {
    day: number;
    time: number;
  };
  expressAddrList?: ExpressAddress[];
  agreeProtocol?: boolean;
  YZShoppingInfo?: {
    waitJoin?: boolean;
    overdueFee?: number;
    overdueLimitFee?: number;
  };
  configErrorDesc?: string;
  configErrorCode?: string;
  brandCode?: string;
  logisticsServicesView?: any;
  expressFee?: number;
  postage?: number;
  expressWayBillAddress?: any;
  allType?: number[];
  allowOpenPrivacy?: boolean;
}

interface ComponentProps {
  express?: ExpressInfo;
  waybillVersion?: number;
  orderNo?: string;
  orderNos?: string[];
  formType?: ExpressWayBillFormType;
  onExpressValueChange?: (values: Partial<ExpressInfo>, isFull?: boolean) => void;
  currentElectronWayBillServiceKdtId?: number;
  currentElectronWayBillServiceKdtName?: string;
  isEditExpress?: boolean;
  getSendType?: (allType: number[], currentExpress: any) => number;
  showPrivacy?: boolean;
  YZShoppingInfo?: {
    waitJoin?: boolean;
    overdueFee?: number;
    overdueLimitFee?: number;
  };
  isCurrentKdtDelivery?: boolean;
}

// 扩展全局window类型
declare global {
  interface Window {
    _global?: {
      expressCompanies?: ExpressCompany[];
      [key: string]: any;
    };
  }
}

const RadioGroup = Radio.Group;

const ElectronWayBillUrl = '/v4/trade/electron-way-bill#/manage';
const ShopAddressCreateUrl = '/v4/shop/setting/shop-address#/create';
const RechargeUrl = '/v4/assets/recharge';

const {
  getAppendServiceList,
  getExpressProductTypes,
  getExpressAbility,
  getWayBillTemplate,
  getExpressBrands,
  getDeliveryFee,
  getDeliveryBatchFee,
  queryDeliveryBizTypeByExpressAndAddress
} = api;

const { FormSelectField, FormNumberInputField } = Form;

const priceSplitFormat = (val: number | string) => {
  let price = val;
  if (typeof price === 'number') {
    price = formatMoney(+val);
  }
  if (!price) {
    return [];
  }
  const [intStr, floatStr = '00'] = price.split('.');
  return [intStr, floatStr.padEnd(2, '0')];
};

// 初始化取件时间信息
const initPickTime = () => {
  const pickDays = [
    {
      value: 0,
      text: '今天'
    },
    {
      value: 1,
      text: '明天'
    },
    {
      value: 2,
      text: '后天'
    }
  ];
  const currentHour = new Date().getHours();
  const end = 17;
  let start = currentHour < 9 ? 9 : plus(currentHour, 1);
  if (start >= end) {
    pickDays.shift();
    start = 9;
  }
  return {
    pickDays,
    pickStart: start,
    pickTime: {
      day: pickDays[0].value,
      time: start === end ? 9 : start
    }
  };
};

/**
 * 系统呼叫物流，电子面单显示
 *
 */
export const ExpressExtraSystemCallBase = (props: ComponentProps) => {
  const {
    express = {},
    waybillVersion,
    currentElectronWayBillServiceKdtId,
    currentElectronWayBillServiceKdtName,
    onExpressValueChange,
    orderNo,
    orderNos,
    isEditExpress,
    formType,
    getSendType,
    showPrivacy
  } = props;
  const [systemCallExpressCompanies, setSystemCallExpressCompanies] = useState(
    getExpressCompaniesCache(currentElectronWayBillServiceKdtId) || []
  );
  const [selectedExpress, setSelectedExpress] = useState<ExpressCompany>({} as ExpressCompany);
  const [appendServiceList, setAppendServiceList] = useState([]);
  const [productTypes, setProductTypes] = useState([]);
  const [expressBrands, setExpressBrands] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [expressFee, setExpressFee] = useState(0);
  const [subsidyFee, setSubsidyFee] = useState(0);
  const [YZExpressFeeList, setYZExpressFeeList] = useState([]);
  const [STAllType, setSTAllType] = useState([]);
  const [printerList, setPrinterList] = useState([]);

  const { pickStart, pickTime, pickDays } = initPickTime();

  const timer = useRef(null);
  const expressCompaniesRef = useRef(systemCallExpressCompanies);

  useEffect(() => {
    expressCompaniesRef.current = systemCallExpressCompanies;
  }, [systemCallExpressCompanies]);

  useEffect(() => {
    const currentExpress =
      systemCallExpressCompanies.find(
        (i) => i.expressId === express.expressId && i.paymentType === express.paymentType
      ) || {};

    // 申通快递 根据所选地址 是否支持免打单强制修改 allType
    if (
      currentExpress.expressId === ST_EXPRESS_CODE &&
      express.paymentType === PaymentTypeEnum.Authority &&
      STAllType.length > 0
    ) {
      currentExpress.allType = STAllType;
    }
    setSelectedExpress(currentExpress);
  }, [express.expressId, express.paymentType, systemCallExpressCompanies, STAllType]);

  // 电子面单排序
  const sortSystemCallExpressCompanies = async (
    systemCallExpressCompanies = [],
    autoSelect = false
  ) => {
    let newSystemCallExpressCompanies = [...systemCallExpressCompanies];
    // 将顺丰强制降级到中通后面， 中通是3100
    newSystemCallExpressCompanies.forEach((item) => {
      if (item.expressId === SF_EXPRESS_CODE && item.paymentType === PaymentTypeEnum.Authority) {
        item.recommendPriority = 3099;
      }
    });
    // 新电子面单之前使用过的服务商展示在最前面逻辑,并默认选中
    let defaultExpressId = '';
    let defaultPaymentType = '';
    let defaultExpress;
    let maxExpress: ExpressCompany = {} as ExpressCompany;
    const defaultExpressKey =
      window.localStorage.getItem(
        `${EXPRESS_LOCAL_STORAGE_KEY}_${currentElectronWayBillServiceKdtId}`
      ) || '';
    if (defaultExpressKey) {
      [defaultExpressId, defaultPaymentType] = defaultExpressKey.split('_');
      defaultExpress = newSystemCallExpressCompanies.find(
        (item) =>
          item.expressId === +defaultExpressId &&
          item.paymentType === +defaultPaymentType &&
          item?.channelStatus !== 2
      );
      [maxExpress] = newSystemCallExpressCompanies.sort(
        (pre, next) => minus(next.recommendPriority, pre.recommendPriority)
      );
    }

    newSystemCallExpressCompanies = newSystemCallExpressCompanies
      .map((item) => ({
        ...item,
        recommendPriority:
          item.expressId === +defaultExpressId && item.paymentType === +defaultPaymentType
            ? plus(maxExpress.recommendPriority || 0, 1)
            : item.recommendPriority
      }))
      .sort((pre, next) => minus(next.recommendPriority, pre.recommendPriority));

    const defaultSelectedExpress = defaultExpress || newSystemCallExpressCompanies[0];
    if (defaultSelectedExpress && autoSelect) {
      onExpressValueChange({
        ...defaultSelectedExpress,
        sendType: defaultSelectedExpress.type,
        weight: defaultSelectedExpress.paymentType === PaymentTypeEnum.Authority ? 1000 : undefined // 有赞寄件重量默认 1kg
      });
      setSystemCallExpressCompanies(newSystemCallExpressCompanies);

      // eslint-disable-next-line no-use-before-define
      handleExpressIdChanged(
        +defaultSelectedExpress.expressId,
        +defaultSelectedExpress.paymentType,
        defaultSelectedExpress
      );
    } else {
      setSystemCallExpressCompanies(newSystemCallExpressCompanies);
    }
    setExpressCompaniesCache(currentElectronWayBillServiceKdtId, newSystemCallExpressCompanies);
    // 每次刷新列表，更新全局缓存
    if (window._global) {
      window._global.expressCompanies = newSystemCallExpressCompanies;
    }
    return newSystemCallExpressCompanies;
  };

  // 获取电子面单物流公司列表
  const fetchElectronCompanies = (autoSelect = false) => {
    const { auditNo = '', weight = '' } = express;
    const params = {
      auditNo,
      weight,
      orderNo,
      retailKdtId: currentElectronWayBillServiceKdtId
    };
    return api
      .getExpressByNewWayBill(params)
      .then((res) => sortSystemCallExpressCompanies(res, autoSelect))
      .catch((err) => {
        Notify.error(err.msg || '获取物流公司列表失败！');
      });
  };

  // 更新快递公司
  const updateDeliveryExpress = () => {
    setTimeout(() => {
      fetchElectronCompanies();
    }, 50);
  };

  const handleFetchAddress = async (params) => {
    const isAuthority = params.paymentType === PaymentTypeEnum.Authority;
    params.retailKdtId = currentElectronWayBillServiceKdtId;
    return api[!isAuthority ? 'getDeliveryAddresses' : 'getAuthorityDeliveryAddresses'](
      params
    ).then((data = []) => {
      let addressList = data;
      addressList = addressList.map((item) => {
        const displayAddress = `${item.provinceName} ${item.cityName} ${item.countyName} ${
          item.address
        } ${item.contactName ? item.contactName : ''} ${
          item.mobile || item.phone ? item.mobile || item.phone : ''
        }`;
        return {
          ...item,
          auditNo: item.id || item.auditNo,
          displayAddress: (
            <PopContainer
              popContent={displayAddress}
              content={
                <div
                  style={{
                    width: 300,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {displayAddress}
                </div>
              }
            />
          )
        };
      });
      onExpressValueChange({ expressAddrList: addressList });
      return addressList;
    });
  };

  // 获取打印机列表
  const fetchPrinterList = () => {
    // 新电子面单-获取菜鸟打印机
    getPrinters().then((printers = []) => {
      setPrinterList(printers);
      const defaultPrinter = getExpressPrinter(currentElectronWayBillServiceKdtId);
      if (defaultPrinter && printers.find((item) => item.id === defaultPrinter)) {
        onExpressValueChange({ printerDeviceNo: defaultPrinter });
      }
    });
  };

  // 初始化面单模板
  const fetchAndInitExpressTemplate = (expressItem) => {
    const storageData = getExpressWayBillStorage(
      currentElectronWayBillServiceKdtId,
      expressItem.expressId,
      expressItem.paymentType
    );
    // 面单模板
    getWayBillTemplate({
      expressId: expressItem.expressId,
      paymentType: expressItem.paymentType,
      brandCode: expressItem.brandCode
    }).then((data) => {
      setTemplates(data || []);
      // 如果有templateUrl缓存并且返回了此数据，则直接调用change事件
      if (
        storageData.eWaybillTemplateId &&
        data.find((item) => item.templateUrl === storageData.eWaybillTemplateId)
      ) {
        onExpressValueChange({ eWaybillTemplateId: storageData.eWaybillTemplateId });
      }
      return data;
    });
  };

  // 获取增值服务
  const fetchExpressAppendService = (expressId, addressVo) => {
    const { brandCode = '', latticePointNo = '' } = addressVo;

    getAppendServiceList({
      onlyAvailable: '1',
      expressId,
      brandCode,
      latticePointNo
    }).then((data) => {
      setAppendServiceList(data || []);
    });
  };

  // 获取产品类型
  const fetchProductTypes = (expressId, paymentType, brandCode = '') => {
    getExpressProductTypes({ expressId, brandCode }).then((data) => {
      setProductTypes(data);
      const storageData = getExpressWayBillStorage(
        currentElectronWayBillServiceKdtId,
        expressId,
        paymentType
      );

      if (data.find((item) => item.value === storageData.productCode)) {
        onExpressValueChange({ productCode: storageData.productCode });
      } else {
        onExpressValueChange({ productCode: '' });
      }
    });
  };
  const fetchExpressFee = useCallback(
    (weight, express) => {
      if (!weight) {
        return;
      }
      const { expressId, auditNo, fakeId, expressAddrList = [], paymentType } = express;
      const addressVo = expressAddrList.find((vo) => vo.auditNo === (auditNo || fakeId));
      if (!addressVo) return;
      const { cityName, countyName, provinceName, address, mobile = '', phone = '' } = addressVo;
      // 批量打单发货单独接口获取总运费
      if (
        paymentType === PaymentTypeEnum.Authority &&
        formType === ExpressWayBillFormType.BatchDeliveryPrint
      ) {
        api[isRetailShop ? 'getBatchDeliveryFeeByRetail' : 'getBatchDeliveryFee']({
          orderNos,
          expressId,
          sendProvince: provinceName,
          retailKdtId: currentElectronWayBillServiceKdtId,
          sendCity: cityName
        }).then((data) => {
          const totalSubsidy = (data.deliveryBatchCalculateFeeList || []).reduce(
            (pre, cur) => plus(cur.benefitSubsidyFee || 0, pre),
            0
          );
          onExpressValueChange({
            expressFee: data.totalPrice
          });
          setExpressFee(data.totalPrice);
          setSubsidyFee(totalSubsidy);
        });
        return;
      }
      getDeliveryFee({
        distWeight: weight,
        orderNo,
        expressId,
        deliveryType: 14,
        province: provinceName,
        city: cityName,
        county: countyName,
        address,
        mobile,
        phone
      })
        .then(({ fee, benefitSubsidyFee = 0 }) => {
          onExpressValueChange({
            postage: fee
          });
          setExpressFee(fee);
          setSubsidyFee(benefitSubsidyFee);
        })
        .catch((err) => {
          Notify.error(err || '获取运费失败');
        });
      getDeliveryBatchFee({
        orderNo,
        weight,
        expressIds: expressCompaniesRef.current
          .filter((res) => res.paymentType === PaymentTypeEnum.Authority)
          .map((res) => res.expressId),
        sendProvince: provinceName,
        sendCity: cityName
      }).then((res) => {
        setYZExpressFeeList(res);
      });
    },
    [onExpressValueChange, orderNo, orderNos, formType, currentElectronWayBillServiceKdtId]
  );
  const hasAbilityCb = (val, currentExpress) => {
    if (timer.current) {
      clearTimeout(timer.current);
    }
    timer.current = window.setTimeout(() => {
      fetchExpressFee(currentExpress.weight, {
        ...currentExpress,
        // 有赞寄件场景下不能传 auditNo
        auditNo: currentExpress.paymentType === PaymentTypeEnum.Authority ? '' : val,
        fakeId: currentExpress.paymentType === PaymentTypeEnum.Authority ? val : '',
        productCode: ''
      });
    }, 500);
  };

  // 校验快递地址可达性
  const fetchExpressAbility = (val: string, currentExpress: any) => {
    const { expressAddrList = [], expressId } = currentExpress;
    const addressVo = expressAddrList.find((vo) => vo.auditNo === val);
    if (!addressVo) {
      return;
    }
    const { address, cityName, countyName, provinceName } = addressVo;
    return getExpressAbility({
      address,
      cityName,
      countyName,
      provinceName,
      expressId
    })
      .then((res) => {
        const { hasAbility = true, reason = '当前发货地址不在快递公司揽收范围内' } = res;
        const newAddresses = Array.from(expressAddrList) as ExpressAddress[];
        const currentAddress = newAddresses.find((vo) => vo.auditNo === val);
        if (currentAddress) {
          currentAddress.hasAbility = hasAbility;
          currentAddress.reason = reason;
        }
        onExpressValueChange({
          expressAddrList: newAddresses
        });
        return res;
      })
      .catch(() => {
        // 接口异常，忽略错误
      });
  };

  // 地址变更
  const handleExpressAddressChange = (val, currentExpress = express) => {
    const { paymentType, expressId, expressAddrList = [] } = currentExpress;

    const addressVo = expressAddrList.find((vo) => vo.auditNo === val);
    if (addressVo) {
      const { address, cityName, countyName, mobile, provinceName, phone, contactName } = addressVo;
      const addressInfo = {
        address,
        city: cityName,
        county: countyName,
        mobile,
        province: provinceName,
        phone,
        senderName: contactName
      };
      onExpressValueChange({
        // 有赞寄件场景下不能传 auditNo
        auditNo: paymentType === PaymentTypeEnum.Authority ? '' : val,
        fakeId: paymentType === PaymentTypeEnum.Authority ? val : '',
        productCode: '',
        expressWayBillAddress: addressInfo
      });
    } else {
      onExpressValueChange({
        auditNo: '',
        fakeId: ''
      });
    }

    // 非官方寄件，需要获取增值服务和顺丰的产品类型
    if (val && paymentType !== PaymentTypeEnum.Authority) {
      if (!addressVo) {
        return;
      }

      // 获取增值服务
      fetchExpressAppendService(expressId, addressVo);
      // 顺丰的产品类型
      if (expressId === SF_EXPRESS_CODE) {
        fetchProductTypes(expressId, paymentType, addressVo.brandCode || '1');
      }
    }
    if (timer.current) {
      clearTimeout(timer.current);
    }

    // 有赞寄件，校验所选地址是否可达
    if (val && paymentType === PaymentTypeEnum.Authority) {
      fetchExpressAbility(val, currentExpress)
        ?.then(({ hasAbility }) => {
          // 地址可揽收则计算运费
          if (hasAbility) {
            hasAbilityCb(val, currentExpress);
          }
        })
        .catch(() => {
          // 接口异常降级为可揽收
          hasAbilityCb(val, currentExpress);
        });
    }
  };

  const handleValidAddress = (
    val: string,
    addresses: any[] = [],
    currentExpress: any = selectedExpress
  ) => {
    const currentAddress = addresses.find((res) => res.id === val);
    if (!currentAddress) return;

    const { cityName, provinceName } = currentAddress;
    const { expressId } = currentExpress;
    const params = { sendCity: cityName, sendProvince: provinceName, expressId };

    queryDeliveryBizTypeByExpressAndAddress(params).then((res) => {
      if (res && Array.isArray(res)) {
        setSTAllType(res);

        const sendType = getSendType(res, currentExpress);
        onExpressValueChange({ sendType, allType: res });
      }
    });
  };

  const initAddressInfo = (expressId, paymentType, currentExpress = express) => {
    const storageData = getExpressWayBillStorage(
      currentElectronWayBillServiceKdtId,
      expressId,
      paymentType
    );

    // 有赞寄件看 fakeId
    const id = paymentType === PaymentTypeEnum.Authority ? storageData.fakeId : storageData.auditNo;

    // 如果有auditNo缓存并且地址返回了此地址，则直接调用change事件
    if (id && currentExpress.expressAddrList?.find((item) => item.auditNo === id)) {
      handleExpressAddressChange(id, currentExpress);
      // 申通有赞寄件，需要校验地址是否可达
      if (expressId === ST_EXPRESS_CODE && PaymentTypeEnum.Authority === paymentType) {
        handleValidAddress(id, currentExpress.expressAddrList, currentExpress);
      }
    } else {
      // 品牌变更后，如果没有默认地址，设置为空
      handleExpressAddressChange('');
    }
  };

  // 快递品牌变更
  const handleExpressBrandChange = (brandCode, currentExpress = express) => {
    const { expressId, paymentType } = currentExpress;
    if (!brandCode) {
      return;
    }
    onExpressValueChange({
      brandCode,
      eWaybillTemplateId: ''
    });
    if (expressId) {
      handleFetchAddress({
        expressId,
        paymentType,
        waybillVersion,
        brandCode
      }).then((addresses) => {
        initAddressInfo(expressId, paymentType, { ...currentExpress, expressAddrList: addresses });
      });

      fetchAndInitExpressTemplate({
        expressId,
        paymentType,
        brandCode
      });
    }
  };

  const fetchAndInitExpressBrand = (currentExpress) => {
    const { expressId, paymentType } = currentExpress;
    const storageData = getExpressWayBillStorage(
      currentElectronWayBillServiceKdtId,
      expressId,
      paymentType
    );
    getExpressBrands({
      expressId,
      onlyAvailable: 1
    }).then((expressBrands) => {
      setExpressBrands(expressBrands || []);
      // 如果有brandCode缓存并且返回了此数据，则直接调用change事件
      if (
        storageData.brandCode &&
        expressBrands.find((item) => item.brandCode === storageData.brandCode)
      ) {
        return handleExpressBrandChange(storageData.brandCode, currentExpress);
      }
      // 如果只有一个，直接选中
      if (expressBrands.length === 1) {
        return handleExpressBrandChange(expressBrands[0].brandCode, currentExpress);
      }
      // 否则，选择默认的顺丰速运
      const item = expressBrands.find((item) => item.isDefault);
      if (item) {
        return handleExpressBrandChange(item.brandCode, currentExpress);
      }
    });
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleExpressIdChanged = (
    expressId: number,
    paymentType: number,
    defaultSelectedExpress: Partial<ExpressCompany> = {}
  ) => {
    if (systemCallExpressCompanies.length === 0 && !defaultSelectedExpress.expressId) return;
    const selectedExpress =
      find(systemCallExpressCompanies, { expressId, paymentType }) || defaultSelectedExpress;
    if (selectedExpress?.channelStatus === 2) return; // 渠道不可用
    setSelectedExpress(selectedExpress);
    setTemplates([]);
    setProductTypes([]);
    setAppendServiceList([]);
    setExpressBrands([]);
    setExpressFee(0);

    const allType = selectedExpress?.allType || [];

    const initParams = {
      sendType: getSendType(allType, selectedExpress),
      expressId: selectedExpress.expressId,
      expressName: selectedExpress.expressName,
      pickTime,
      isPay: selectedExpress.isPay,
      auditNo: '', // 快递公司变更，auditNo重置
      eWaybillTemplateId: '', // 面单模板重置
      fakeId: '',
      logisticsServicesView: {}, // 增值服务重置
      productCode: '', // 产品类型重置
      brandCode: '', // 品牌
      expressFee: 0, // 快递预估价设置为0
      weight: selectedExpress.paymentType === PaymentTypeEnum.Authority ? 1000 : undefined, // 有赞寄件重量默认 1kg
      depositOffline: selectedExpress.depositOffline || true,
      latticePointDetailModel: selectedExpress.latticePointDetailModel || [],
      paymentType: selectedExpress.paymentType,
      receiveTimeRemark: selectedExpress.receiveTimeRemark
    };
    onExpressValueChange(initParams);

    const currentExpress = { ...initParams };
    const isSfOneself = expressId === SF_EXPRESS_CODE && paymentType === PaymentTypeEnum.Oneself;
    // 新电子面单顺丰不请求地址，因为还需要依赖品牌的选择
    if (!isSfOneself) {
      handleFetchAddress({
        expressId: selectedExpress.expressId,
        paymentType: selectedExpress.paymentType,
        waybillVersion,
        brandCode: selectedExpress.brandCode
      }).then((addresses) => {
        (currentExpress as any).expressAddrList = addresses;
        initAddressInfo(expressId, paymentType, currentExpress);
      });
    }

    // 顺丰需要选择品牌后请求面单模板
    if (!isSfOneself) {
      // 面单模板
      fetchAndInitExpressTemplate({
        ...selectedExpress,
        // 有赞寄件顺丰写死不用选快递品牌，写死 'SF' 即可，获取面单模板列表时要过滤出顺丰速运的
        brandCode: expressId === SF_EXPRESS_CODE ? 'SF' : ''
      });
    } else {
      // 快递品牌
      fetchAndInitExpressBrand(currentExpress);
    }

    updateDeliveryExpress();
  };

  useEffect(() => {
    fetchPrinterList();
    fetchElectronCompanies(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentElectronWayBillServiceKdtId]);

  const { configErrorDesc, configErrorCode, YZShoppingInfo } = express;
  const errorLinkInfo =
    configErrorCode === String(ELECTRON_WAYBILL_SERVICE_CLOSED)
      ? null
      : ELECTRON_ERROR_INFO[configErrorCode as any];
  const renderPickTime = () => {
    const pickTime = express.pickTime || { day: 0, time: 0 };
    const end = 17;
    const start = pickTime.day === 0 ? pickStart || 9 : 9;
    const pickTimes = new Array(minus(end, start)).fill(0).map((_, index) => ({
      value: plus(start, index),
      text: `${prefixZero(plus(start, index))}:00 - ${prefixZero(plus(start, plus(index, 1)))}:00`
    }));
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          {!find(EXPRESS_SUPPORT_DETAIL_PICK_TIME, (id) => id === selectedExpress.expressId) ? (
            <FormSelectField
              key="pickTime"
              label="取件时间："
              name="pickTime"
              value={0}
              data={[
                {
                  value: 0,
                  text: '尽快上门取件'
                }
              ]}
            />
          ) : (
            <div className="pick-time-field">
              <FormSelectField
                className="inline"
                label="取件时间："
                data={pickDays}
                name="pickDay"
                value={pickTime.day}
                onChange={(val) =>
                  onExpressValueChange({
                    pickTime: {
                      ...express.pickTime,
                      day: val
                    }
                  })
                }
                validations={{ required: true }}
                validationErrors={{ required: '请选择取件时间' }}
              />
              <Select
                value={pickTime.time}
                data={pickTimes}
                onChange={(e) =>
                  onExpressValueChange({
                    pickTime: {
                      ...express.pickTime,
                      time: +e.target.value
                    }
                  })
                }
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  // 面单模板
  const renderPrintTemplate = () => {
    const { eWaybillTemplateId } = express;

    const templatesView = templates.map((item) => {
      return {
        ...item,
        label: (
          <PopContainer
            popContent={item.templateName}
            content={
              <div
                style={{
                  width: 180,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {item.templateName}
              </div>
            }
          />
        )
      };
    });
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <FormSelectField
            className="inline"
            autoWidth
            width="200px"
            name="eWaybillTemplateId"
            label="面单模板："
            value={eWaybillTemplateId}
            data={templatesView}
            required={express.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value}
            optionText="label"
            optionValue="templateUrl"
            validations={{ required: true }}
            validationErrors={{ required: '请选择面单模板' }}
            onChange={(val) => {
              onExpressValueChange({ eWaybillTemplateId: val });
            }}
          />
        </div>
      </div>
    );
  };

  const getAddressManagePageText = (len, paymentType) => {
    if (len === 0) {
      return '去开通';
    }
    if (paymentType === PaymentTypeEnum.Oneself) {
      return '管理发货地址';
    }
    return '新增发货地址';
  };

  const getAddressManagePageUrl = (autoOpen) => {
    const { expressId, paymentType } = selectedExpress;
    const rootUrl =
      // eslint-disable-next-line no-nested-ternary
      paymentType === PaymentTypeEnum.Oneself ? ElectronWayBillUrl : ShopAddressCreateUrl;
    if (autoOpen) {
      return `${rootUrl}?code=${expressId}&paymentType=${paymentType}&scene=OPEN_SERVICE`;
    }
    return rootUrl;
  };

  /** 是否是当前店铺发货,排除总部帮仓库等发货的情况 */
  const isCurrentKdtDelivery = currentElectronWayBillServiceKdtId === window._global.kdtId;
  let selectedAddressDisabled = (express.expressAddrList || [])?.length === 0;
  selectedAddressDisabled =
    selectedAddressDisabled ||
    (YZShoppingInfo?.waitJoin && selectedExpress?.recommend && !express.agreeProtocol);

  let abilityErrorMsg = '';
  const currentAddress = (express.expressAddrList || []).find(
    (res) => res.auditNo === (express.auditNo || express.fakeId)
  );
  const hasAbility = currentAddress?.hasAbility;
  if (!hasAbility) {
    abilityErrorMsg = currentAddress?.reason || '';
  }

  const handleNewBillWayWeightChange = (value) => {
    const weight = times(+value, 1000);
    const isPay = selectedExpress.expressId && selectedExpress.isPay;
    onExpressValueChange({ weight });
    if (isPay) {
      // 申通快递灰度中，更新快递列表
      updateDeliveryExpress?.();
      if (timer.current) {
        clearTimeout(timer.current);
      }
      timer.current = window.setTimeout(() => {
        fetchExpressFee(weight, express);
      }, 500);
    }
  };

  // 快递品牌
  const renderExpressBrand = () => {
    const { brandCode } = express;
    return (
      <div className="express-content-item">
        <div className="express-content-item__label">快递品牌：</div>
        <RadioGroup
          value={brandCode}
          onChange={(e) => handleExpressBrandChange(e.target.value, express)}
        >
          {expressBrands.map((item) => {
            return (
              <Radio key={item.brandCode} value={item.brandCode}>
                {item.brandName}
              </Radio>
            );
          })}
        </RadioGroup>
      </div>
    );
  };

  // 新电子面单发货类型
  const renderExpressWayBillTypeSelection = () => {
    if (!selectedExpress.allType) {
      return null;
    }
    return (
      <div className="express-content-item">
        <div className="express-content-item__label">发货类型：</div>
        <RadioGroup
          value={express.sendType}
          onChange={(e) => onExpressValueChange({ sendType: +e.target.value })}
        >
          {selectedExpress.allType.map((type) => {
            const key = EXPRESS_WAY_BILL_TYPES_MAP[type];
            return (
              <Radio
                key={type}
                value={EXPRESS_WAY_BILL_TYPES[key].value}
                style={{ verticalAlign: 'top' }}
              >
                {EXPRESS_WAY_BILL_TYPES[key].text}
                {type === 4 && <div className="text-gray">单日发货5单以上建议自己打印面单</div>}
              </Radio>
            );
          })}
        </RadioGroup>
      </div>
    );
  };

  // 新电子面单-打印机
  const renderPrinterSelection = () => {
    const noPrinter = printerList.length === 0;
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <FormSelectField
              className="inline"
              autoWidth
              width="200px"
              name="printer"
              label="打印机："
              data={printerList}
              value={express.printerDeviceNo}
              required={express.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value}
              disabled={noPrinter}
              optionText="name"
              optionValue="id"
              onChange={(val) => {
                setExpressPrinter(currentElectronWayBillServiceKdtId, val);
                onExpressValueChange({ printerDeviceNo: val });
              }}
              validations={{ required: true }}
              validationErrors={{ required: '请选择一台打印机' }}
            />
            <span>
              <a style={{ cursor: 'pointer' }} onClick={() => fetchPrinterList()}>
                刷新
              </a>
            </span>
          </div>
          {noPrinter && (
            <div className="express-way-bill-cainiao-printer-tips">
              未识别到可选择的打印机，请先<strong>打开云打印组件；</strong>如果未安装组件，请先
              <a
                // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
                href="https://page.cainiao.com/waybill/cloud_printing/home.html?spm=a262a.ap-detail.0.0.5aec3651pQobvi"
                target="_blank"
                rel="noreferrer"
              >
                下载安装
              </a>
              。
              <a
                href={setUrlDomain('/displaylist/detail_4_4-2-85545', 'help')}
                target="_blank"
                rel="noreferrer"
              >
                查看打印组件使用教程
              </a>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 新电子面单-产品类型
  const renderProductType = () => {
    const { productCode } = express;
    // 非自结算顺丰不展示
    if (
      !(
        selectedExpress.expressId === SF_EXPRESS_CODE &&
        selectedExpress.paymentType === PaymentTypeEnum.Oneself
      )
    ) {
      return null;
    }
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <FormSelectField
            className="inline"
            autoWidth
            width="200px"
            name="productCode"
            label="产品类型："
            data={productTypes}
            value={productCode}
            optionText="label"
            optionValue="value"
            resetOption
            resetText="重置"
            onChange={(val) => onExpressValueChange({ productCode: val || '' })}
          />
        </div>
      </div>
    );
  };

  // 新电子面单-增值服务
  const renderAppendService = () => {
    // 官方寄件不展示 || 没有可选增值服务不展示
    if (appendServiceList.length === 0 || express.paymentType === PaymentTypeEnum.Authority) {
      return null;
    }
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <AppendServiceSelection
            value={express.logisticsServicesView}
            dataSource={appendServiceList}
            onChange={(val) => {
              onExpressValueChange({
                logisticsServicesView: val
              });
            }}
          />
        </div>
      </div>
    );
  };

  // 新电子面单-包裹重量
  const renderPackageWeight = () => {
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <FormNumberInputField
            onChange={handleNewBillWayWeightChange}
            value={1}
            min={1}
            max={30}
            decimal={1}
            showStepper
            name="weight"
            label="包裹重量："
            width="160px"
            required
            validations={{
              required: true
            }}
            validationErrors={{
              required: '请填写物品重量'
            }}
            className="delivery-content__express-weight-form"
            addonAfter="kg"
          />
        </div>
      </div>
    );
  };

  const renderPredicatePrice = () => {
    const isPay = selectedExpress.expressId && selectedExpress.isPay;
    const { overdueFee, overdueLimitFee } = express.YZShoppingInfo || {};

    if (!isPay) {
      return null;
    }

    const fee = expressFee ? div(expressFee, 100).toFixed(2) : 0;

    const getFeeDesc = getFeeDescMap[formType] || getFeeDescMap[ExpressWayBillFormType.Normal];
    const feeDesc = isEditExpress
      ? '此为单个包裹预估费用，快递公司称重后，有赞将按照实际计费重量扣费'
      : getFeeDesc();

    return (
      <div className="express-way-bill-fee-line">
        <span className="express-way-bill-fee-line_label">预估费用：</span>
        <span className="express-way-bill-fee-line_content">
          <span className="express-way-bill-fee-line_content-price">{fee} 元</span>（{feeDesc}）
        </span>
        {subsidyFee ? (
          <div className="express-way-bill-fee-line_subsidy">运费补贴{div(subsidyFee, 100)}元</div>
        ) : null}
        {overdueFee >= overdueLimitFee && (
          <div className="express-way-bill-fee-line_balance-warn">
            有赞寄件待结算费用：{formatMoney(overdueFee)}元，请尽快<a href={RechargeUrl}>充值</a>
          </div>
        )}
      </div>
    );
  };

  const renderPrivacyField = () => {
    return (
      <PrivacyFormField
        pageSource={OPEN_EXPRESS_PAGE_SOURCE}
        onCheckedChange={(checked) => {
          onExpressValueChange({
            allowOpenPrivacy: checked
          });
        }}
      />
    );
  };

  let showRest = selectedExpress.expressId > 0 && !!(express.auditNo || express.fakeId);
  const { isPay } = selectedExpress;
  if (isPay) {
    // 有赞寄件，未开通且未勾选开通的不展示更多表单项
    if (express.YZShoppingInfo?.waitJoin && !express.agreeProtocol) {
      showRest = false;
    }
  }

  const isBatchDeliveryPrintAuthority =
    formType === ExpressWayBillFormType.BatchDeliveryPrint &&
    express.paymentType === PaymentTypeEnum.Authority;
  const needShowWayBillType =
    !isBatchDeliveryPrintAuthority ||
    (isBatchDeliveryPrintAuthority &&
      express.paymentType === PaymentTypeEnum.Authority &&
      [JD_EXPRESS_CODE, SF_EXPRESS_CODE, ST_EXPRESS_CODE].includes(express.expressId));
  // 没有返回快递公司时不展示
  if (systemCallExpressCompanies.length === 0) {
    return null;
  }
  return (
    <div>
      <div className="express-content-item">
        <div className="express-content-item__label">快递公司：</div>
        <div className="express-content-item__value">
          <div className="express-content-item__express-company-container">
            {systemCallExpressCompanies.map((item) => {
              let originPrice = item.logisticsRecommendInfo?.originPrice;
              let realPrice = item.logisticsRecommendInfo?.estimateAmount;
              // 新电子面单-有赞寄件，展示根据地址和重量新算的运费
              if (item.paymentType === PaymentTypeEnum.Authority) {
                const currentExpress = YZExpressFeeList.find(
                  (res) => res.expressId === +item.expressId
                );
                if (currentExpress) {
                  originPrice = formatMoney(currentExpress.originPrice);
                  realPrice = formatMoney(currentExpress.realPrice);
                }
              }
              // 批量打单发货场景的快递价格提示
              const batchDeliveryPrintRecommend =
                item.logisticsRecommendInfo?.batchDeliveryPrintRecommend || '';
              return (
                <Tooltip
                  trigger={item?.channelStatus === 2 ? 'hover' : 'focus'}
                  position="bottom-left"
                  title="接口升级中，暂不可用"
                >
                  <div
                    key={`${item.expressId}_${item.paymentType}`}
                    className={`express-content-item__express-company-item ${
                      item.expressId === express.expressId &&
                      item.paymentType === express.paymentType
                        ? 'express-content-item__express-company-item-checked'
                        : ''
                    }
                    ${item?.channelStatus === 2 ? 'disabled' : ''}
                    `}
                    onClick={() => {
                      onExpressValueChange({
                        expressId: item.expressId,
                        paymentType: item.paymentType
                      });
                      handleExpressIdChanged(+item.expressId, item.paymentType);
                    }}
                  >
                    <div className="title">
                      {item.expressName}{' '}
                      {item.recommend && <span className="recommend">有赞寄件</span>}
                    </div>
                    {formType === ExpressWayBillFormType.BatchDeliveryPrint && (
                      <RichTextPreview tag="div" content={batchDeliveryPrintRecommend} />
                    )}
                    {formType !== ExpressWayBillFormType.BatchDeliveryPrint && realPrice && (
                      <div className="tips">
                        <span>预估：</span>
                        <span className="text-price">
                          <span>¥</span>
                          <span className="text-price-int">{priceSplitFormat(realPrice)[0]}</span>
                          <span className="text-price-float">
                            .{priceSplitFormat(realPrice)[1]}
                          </span>
                        </span>
                        {originPrice && Number(realPrice) < Number(originPrice) && (
                          <span className="text-origin-price">
                            ¥{priceSplitFormat(originPrice).join('.')}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </Tooltip>
              );
            })}
          </div>
          {isCurrentKdtDelivery && (
            <div className="express-content-item__express-company-action">
              <a href="/v4/trade/electron-way-bill#/manage?scene=OPEN_SERVICE" target="_blank">
                添加服务商
              </a>
              <a
                onClick={() => {
                  // 服务商刷新，先清空服务商信息，刷新服务商数据后，根据目前选中的默认服务商重新执行选择逻辑
                  onExpressValueChange(
                    {
                      ...defaultExpressData.express,
                      pickTime,
                      YZShoppingInfo
                    },
                    true
                  );

                  setSelectedExpress({} as ExpressCompany);

                  fetchElectronCompanies().then(() => {
                    const { expressId, paymentType } = express;
                    if (express.expressId) {
                      handleExpressIdChanged(+expressId, +paymentType);
                    }
                  });
                }}
              >
                刷新
              </a>
            </div>
          )}
          {YZShoppingInfo?.waitJoin &&
            selectedExpress.recommend &&
            (isCurrentKdtDelivery ? (
              <>
                <Checkbox
                  className="express-content-item__express-company-protocol"
                  checked={express.agreeProtocol}
                  onChange={(e) => {
                    onExpressValueChange({ agreeProtocol: e.target.checked });
                  }}
                >
                  <div>
                    开通有赞寄件{' '}
                    <span style={{ color: '#ED6A18' }}>
                      无需预充值，单单享运费补贴！
                      <a
                        href={setUrlDomain(
                          '/v4/assets/shipping?source=order-delivery-dialog',
                          'store'
                        )}
                        target="_blank"
                        rel="noreferrer"
                      >
                        查看详情
                      </a>
                    </span>
                  </div>
                  <div style={{ color: '#999' }}>
                    勾选即表示已阅读并同意
                    <a
                      href={setUrlDomain(
                        '/intro/rule/detail?alias=js9mzaf9&pageType=agreements',
                        'youzan'
                      )}
                      target="_blank"
                      rel="noreferrer"
                    >
                      《有赞寄件服务协议》
                    </a>
                    ，同意开通有赞寄件统一接入服务商，预计费用详见
                    <a href={selectedExpress.freightStandardUrl} target="_blank" rel="noreferrer">
                      运费说明
                    </a>
                  </div>
                </Checkbox>
                {!express.agreeProtocol && (
                  <p style={{ color: '#D42F15', fontSize: '12px' }}>请勾选“开通有赞寄件”</p>
                )}
              </>
            ) : (
              !!currentElectronWayBillServiceKdtName && (
                <div
                  className="express-content-item__express-company-protocol"
                  style={{ color: '#ED6A18' }}
                >
                  {`店铺：${currentElectronWayBillServiceKdtName}未开通有赞寄件服务，请先开通`}
                </div>
              )
            ))}
        </div>
      </div>

      {selectedExpress.expressId === SF_EXPRESS_CODE &&
        selectedExpress.paymentType === PaymentTypeEnum.Oneself &&
        expressBrands.length > 0 &&
        renderExpressBrand()}

      {isNumber(express.expressId) && (
        <>
          <div className="express-content-item">
            <div className="express-content-item__label">发货地址：</div>
            <div className="express-content-item__value">
              <Select
                data={express.expressAddrList || []}
                optionValue="auditNo"
                optionText="displayAddress"
                className={cx('express-address', {
                  'error-field': abilityErrorMsg
                })}
                autoWidth
                placeholder="请选择发货地址"
                onChange={(e) => {
                  updateDeliveryExpress();
                  handleExpressAddressChange(e.target.value);
                  if (
                    selectedExpress?.expressId === ST_EXPRESS_CODE &&
                    selectedExpress?.paymentType === PaymentTypeEnum.Authority
                  ) {
                    handleValidAddress(e.target.value, express.expressAddrList || []);
                  }
                }}
                value={express.auditNo || express.fakeId}
                disabled={selectedAddressDisabled}
              />
              {isCurrentKdtDelivery && (
                <>
                  <BlankLink
                    href={getAddressManagePageUrl((express.expressAddrList || [])?.length || 0)}
                  >
                    {getAddressManagePageText(
                      (express.expressAddrList || [])?.length || 0,
                      express.paymentType
                    )}
                  </BlankLink>
                  <BlankLink
                    onClick={() => {
                      handleFetchAddress({
                        expressId: express.expressId,
                        paymentType: express.paymentType,
                        waybillVersion,
                        brandCode: express.brandCode
                      });
                    }}
                    style={{ marginLeft: '10px' }}
                  >
                    刷新
                  </BlankLink>
                </>
              )}

              {abilityErrorMsg && <p className="error-info">{abilityErrorMsg}</p>}
              {/**
               * 1、新的发货弹窗；
               * 2、分店开了有赞寄件；
               * 3、总店帮助分店发货；
               * 4、分店发货地址为空；
               * */}
              {!(YZShoppingInfo?.waitJoin && selectedExpress.recommend) &&
                !isCurrentKdtDelivery &&
                (express.expressAddrList || [])?.length === 0 && (
                  <div style={{ color: '#ED6A18', marginTop: 10 }}>
                    履约店铺未设置发货地址，请通知履约店铺在地址库中添加
                  </div>
                )}
            </div>
          </div>

          {showRest && needShowWayBillType && renderExpressWayBillTypeSelection()}
          {
            // 这里的是配置异常
            errorLinkInfo && (
              <div className="postage-info grey">
                {configErrorDesc}，
                <BlankLink href={errorLinkInfo.link}>{errorLinkInfo.text}</BlankLink>
              </div>
            )
          }

          {showRest && (
            <>
              {((express.sendType === 2 && express.paymentType !== PaymentTypeEnum.Authority) ||
                (express.paymentType === PaymentTypeEnum.Authority &&
                  selectedExpress.expressId === SF_EXPRESS_CODE)) &&
                renderPickTime()}
              {isPay && renderPackageWeight()}
              {express.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value &&
                renderPrintTemplate()}
              {renderProductType()}
              {renderAppendService()}
              {express.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value &&
                renderPrinterSelection()}
              {isPay && renderPredicatePrice()}
              {showPrivacy && renderPrivacyField()}
            </>
          )}
        </>
      )}
    </div>
  );
};

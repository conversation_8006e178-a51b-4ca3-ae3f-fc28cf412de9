/**
 * 聚合了所有外部依赖的扩展能力，同时会在index.tsx中注册
 */
import { getExpressPostData } from './get-express-post-data';
import { setDefaultExpressLocalStorage } from './set-default-express-local-storage';
import { checkExpressValid } from './check-express-valid';
import { beforeConfirmShipment } from './before-confirm-shipment';
import { renderFooterExtra } from './render-footer-extra';

export {
  getExpressPostData,
  setDefaultExpressLocalStorage,
  checkExpressValid,
  beforeConfirmShipment,
  renderFooterExtra
};

const checkDeliveryAddress = (express) => {
  const currentAddress = express.expressAddrList?.find(
    (res) => res.auditNo === (express.auditNo || express.fakeId)
  );
  const hasAbility = currentAddress?.hasAbility;
  if (hasAbility === false && currentAddress?.reason) {
    return false;
  }
  return true;
};

/**
 * 电子面单是否有效
 * 1. 检查地址是否不可用
 * 2. 检查有赞寄件的场景保证金是否可用
 * @param express 电子面单
 * @returns {boolean}
 */
export const checkExpressValid = (express) => {
  let isValid = checkDeliveryAddress(express);
  if (express?.isPay) {
    isValid = isValid && express.depositOffline;
  }
  return isValid;
};

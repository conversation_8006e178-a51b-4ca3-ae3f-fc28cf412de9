import {
  setExpressWayBillStorage,
  EXPRESS_LOCAL_STORAGE_KEY,
  setWayBillTypeStorage,
  setExpressPrinter
} from '../common/storage';

interface IExtOptions {
  kdtId: number; // 当前电子面单服务商kdtId
}

// 缓存数据
export const setDefaultExpressLocalStorage = (express, options: IExtOptions) => {
  const { kdtId } = options;
  const {
    expressId,
    paymentType,
    sendType,
    eWaybillTemplateId = '',
    templateUrl = '',
    auditNo,
    fakeId,
    productCode = '',
    brandCode = '',
    printerDeviceNo
  } = express;
  if (expressId) {
    window.localStorage.setItem(
      `${EXPRESS_LOCAL_STORAGE_KEY}_${kdtId}`,
      `${expressId}_${paymentType}`
    );
  }
  setExpressWayBillStorage(kdtId, expressId, paymentType, {
    eWaybillTemplateId: eWaybillTemplateId || templateUrl,
    auditNo,
    fakeId,
    productCode,
    brandCode
  });
  setWayBillTypeStorage(kdtId, sendType);
  setExpressPrinter(kdtId, String(printerDeviceNo));
};

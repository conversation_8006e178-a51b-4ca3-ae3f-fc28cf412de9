import { isRetailShop } from '@youzan/utils-shop';
import addDays from 'date-fns/add_days';
import getYear from 'date-fns/get_year';
import getMonth from 'date-fns/get_month';
import getDate from 'date-fns/get_date';
import setHours from 'date-fns/set_hours';
import getTime from 'date-fns/get_time';
import { EXPRESS_WAY_BILL_TYPES, SF_EXPRESS_CODE, expressFieldMapping } from '../common/constants';

interface IExtOptions {
  kdtId: number; // 当前电子面单服务商kdtId
}

export function formatExpressData(express) {
  const { pickTime, logisticsServicesView, ...rest } = express;
  const { expressId, sendType } = rest;
  rest.waybillVersion = 2;
  if (
    pickTime &&
    expressId === SF_EXPRESS_CODE &&
    (sendType === EXPRESS_WAY_BILL_TYPES.printAndCallCourier.value ||
      sendType === EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value)
  ) {
    const now = new Date();
    // 后端需要时间精确到秒
    const startAppointment =
      getTime(
        addDays(
          // eslint-disable-next-line @youzan/yz-retail/no-new-date
          setHours(new Date(getYear(now), getMonth(now), getDate(now)), pickTime.time),
          pickTime.day as number
        )
      ) / 1000;
    rest.startTime = startAppointment;
    rest.endTime = startAppointment + 3600;
  }
  // 增值服务数据格式处理
  if (logisticsServicesView) {
    rest.logisticsServices = JSON.stringify(logisticsServicesView);
  }
  const deleteKeys = ['expressAddrList', 'receiveTimeRemark'];
  deleteKeys.forEach((key) => {
    delete rest[key];
  });
  return rest;
}

function formatExpressForRetail(express, options: IExtOptions) {
  const result = { ...express };
  result.waybillVersion = 2;
  result.providerKdtId = options.kdtId;
  return result;
}

function formatExpressForWsc(express) {
  const { weight, ...result } = express;
  const retailFieldKeys = Object.keys(expressFieldMapping);
  retailFieldKeys.forEach((key) => {
    delete result[key];
  });
  if (weight) {
    result.weight = weight;
  }
  // 微商城电子面单字段转换
  if (result.startTime) {
    result.startAppointment = result.startTime;
  }
  if (result.endTime) {
    result.endAppointment = result.endTime;
  }
  return result;
}

/**
 * 获取电子面单数据
 * @param {*} express
 */
export function getExpressPostData(express, options: IExtOptions) {
  const nextExpress = formatExpressData(express);
  if (isRetailShop) {
    return formatExpressForRetail(nextExpress, options);
  }
  return formatExpressForWsc(nextExpress);
}

import { isRetailShop } from '@youzan/utils-shop';
import { setUrlDomain } from '@youzan/retail-utils';
import { OPEN_EXPRESS_PAGE_SOURCE } from '../common/constants';
import { applyOpenPrivacy, applyOpenYzAuthority } from '../common/api';
import openRequire from '../../apply-require';
import { PaymentTypeEnum } from '../types';

/**
 * 检查是否需要开通隐私面单
 * @param express
 */
const handlePrivacyOpen = (express) => {
  const { allowOpenPrivacy } = express;
  if (allowOpenPrivacy) {
    applyOpenPrivacy({
      source: OPEN_EXPRESS_PAGE_SOURCE
    });
  }
};

/**
 * 检查是否需要申请有赞寄件权限
 * @param express
 */
const checkApplyYzAuthority = async (express) => {
  const { paymentType, agreeProtocol, YZShoppingInfo } = express;
  const needOpenYz =
    paymentType === PaymentTypeEnum.Authority && YZShoppingInfo?.waitJoin && agreeProtocol;
  if (!needOpenYz) {
    return Promise.resolve();
  }
  return applyOpenYzAuthority({
    pageSource: 'order-delivery-dialog'
  }).then((res) => {
    const { decisionResult, passedCert, shopAbilityValid } = res;
    const shopTypeText = isRetailShop ? '零售单店或连锁L' : '微商城单店';
    if (!decisionResult) {
      openRequire([
        {
          conditionDesc: `店铺类型为${shopTypeText}，且处于有效期`,
          pass: shopAbilityValid
        },
        {
          conditionDesc: '店铺已完成主体认证',
          pass: passedCert,
          unPassDesc: '去完成',
          link: setUrlDomain('/v4/cert/principal/intro?from=order-delivery', 'store')
        }
      ]);
      return Promise.reject();
    }
    return Promise.resolve();
  });
};

/**
 * 确认发货前的操作，可终端发货
 * @param express
 */
export const beforeConfirmShipment = async (express) => {
  // 微商城才需要开通隐私面单
  if (!isRetailShop) {
    handlePrivacyOpen(express);
  }

  await checkApplyYzAuthority(express);
};

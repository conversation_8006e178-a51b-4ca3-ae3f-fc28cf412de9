export enum BindStatus {
  Success = 0,
  Waiting = 1,
  Failed = 2,
  Unbind = 3
}

export interface IWechatServiceType {
  // 服务类型ID
  serviceType: number;
  // 服务类型名称
  serviceName: string;
}

export interface ISenderAddress {
  // 联系人
  contactUserName: string;
  // 详细地址
  address: string;
  // 市
  cityName: string;
  // 联系电话
  contactUserPhone: string;
  // 省
  provinceName: string;
  // 县/区
  countyName: string;
  // 完整地址
  name?: string;
}

export interface IWechatDeliveryBindAccount {
  // 该账号已选择的服务类型
  selectedServiceType: IWechatServiceType;
  // 该账号关联的发货地址
  addressDTO: ISenderAddress;
  // 商家绑定的快递账号在物流侧的编码  由logistics生产、维护
  bindNo: string;
  // 商家在快递公司的客户编码(或者账号)
  bizId: string;
  // 快递公司账号绑定审核状态
  bindStatusMsg: string;
  bindStatusCode: BindStatus;
}

export interface IWechatDeliveryExpress {
  // 快递公司名称
  expressName: string;
  deliveryBindAccountDTOS: IWechatDeliveryBindAccount[];
  // 该快递公司支持的所有服务类型
  allSupportedServiceTypes: IWechatServiceType[];
  // 快递公司ID
  expressId: number;
}

export interface IWechatExpressWayBill {
  expressId?: number;
  expressName?: string;
  accountNo?: string;
  weight?: number;
}

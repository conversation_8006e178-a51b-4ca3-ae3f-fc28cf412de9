import * as React from 'react';
import { Select } from '@zent/compat';
import { BlankLink } from '@youzan/react-components';
import { IWechatExpressWayBill, IWechatDeliveryExpress, BindStatus } from './type';

const WEIXIN_DELIVERY_HELPER = '/v4/trade/wechat-delivery-helper';

interface IWeChatDeliveryProps {
  availableWechatExpress: IWechatDeliveryExpress[];
  onChange: (val: IWechatExpressWayBill) => void;
}

interface ISelectItem {
  value: string;
  text: string;
}

interface IWeixinDeliveryState {
  expressId: number;
  accountNos: ISelectItem[];
  accountNo: string;
}

class WeixinDelivery extends React.Component<IWeChatDeliveryProps, IWeixinDeliveryState> {
  constructor(props) {
    super(props);
    this.state = {
      expressId: 0,
      accountNos: [],
      accountNo: ''
    };
  }

  handleExpressIdChanged = (evt, { expressName }: IWechatExpressWayBill) => {
    const expressId = evt.target.value;
    const { availableWechatExpress, onChange } = this.props;
    const accountNos: ISelectItem[] = [];
    availableWechatExpress.forEach(express => {
      if (express.expressId === expressId) {
        const accounts = express.deliveryBindAccountDTOS || [];
        accounts.forEach(account => {
          const { provinceName, cityName, countyName, address } = account.addressDTO || {};
          const success = account.bindStatusCode === BindStatus.Success;
          if (provinceName && cityName && countyName && address && success) {
            accountNos.push({
              value: account.bindNo,
              text: `${provinceName}${cityName}${countyName}${address}`
            });
          }
        });
      }
    });
    this.setState({ expressId, accountNos, accountNo: '' });
    onChange({ expressId, expressName });
  };

  handlewaybillAccountNoChanged = evt => {
    const accountNo = evt.target.value;
    const { onChange } = this.props;
    this.setState({ accountNo });
    onChange({ accountNo });
  };

  render() {
    const { availableWechatExpress } = this.props;
    const { expressId, accountNos, accountNo } = this.state;
    return (
      <div className="delivery-content">
        <div className="express-content-item">
          <div className="express-content-item__label">快递公司：</div>
          <div className="express-content-item__value">
            <Select
              data={availableWechatExpress}
              value={expressId}
              onChange={this.handleExpressIdChanged}
              optionText="expressName"
              optionValue="expressId"
            />
            <BlankLink href={WEIXIN_DELIVERY_HELPER}>去设置</BlankLink>
          </div>
        </div>
        <div className="express-content-item">
          <div className="express-content-item__label">发货地址：</div>
          <div className="express-content-item__value">
            <Select
              data={accountNos}
              value={accountNo}
              onChange={this.handlewaybillAccountNoChanged}
            />
          </div>
        </div>
      </div>
    );
  }
}

export default WeixinDelivery;

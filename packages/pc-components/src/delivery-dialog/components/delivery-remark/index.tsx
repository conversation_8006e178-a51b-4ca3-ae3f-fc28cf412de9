import * as React from 'react';
import { Input } from 'zent';

import { ExpressType } from '@youzan/order-domain-definitions/es/order';

interface IDeliveryRemarkProps {
  expressType: number;
  value: string;
  onChange: (value: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const { Express, SelfFetch } = ExpressType;

export function DeliveryRemark({ value, onChange, expressType }: IDeliveryRemarkProps) {
  if ([SelfFetch, Express].includes(expressType)) {
    return null;
  }

  return (
    <div className="od-delivery-remark-container">
      <div className="delivery-extra-content__label">发货备注：</div>
      <div className="od-delivery-remark-content">
        <Input
          value={value}
          onChange={onChange}
          type="textarea"
          maxLength={256}
          placeholder="使用三方服务商时，该备注会显示给骑手"
          showCount
          autoSize
        />
      </div>
    </div>
  );
}

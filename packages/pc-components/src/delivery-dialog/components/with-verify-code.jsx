import React from 'react';
import { Input } from 'zent';

const WithoutVerifyCode = ({ selfFetch, onChangeInput }) => {
  return (
    <div className="express-content-item">
      <div className="express-content-item__label">
        {/* eslint-disable-next-line no-nested-ternary */}
        {'提货码：'}
      </div>

      <Input
        value={selfFetch.selfFetchNo}
        onChange={({ target: { value: selfFetchNo } }) => {
          /** 匹配核销码 */
          const reg = /^[0-9a-zA-Z]*$/;
          if (!reg.test(selfFetchNo)) return;
          onChangeInput({
            selfFetch: {
              ...selfFetch,
              selfFetchNo: selfFetchNo.trim()
            }
          });
        }}
      />
    </div>
  );
};

export default WithoutVerifyCode;

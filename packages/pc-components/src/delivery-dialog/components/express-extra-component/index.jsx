import React from 'react';
import { isRetailSingleStore } from '@youzan/utils-shop';
import { Notify } from 'zent';
import { cloneDeep, find, get, isEmpty, isNil } from 'lodash';
import { debounce } from '@youzan/retail-armor';
import { StorageKeyJdExpressLastEWaybillTemplateId } from '@youzan/order-domain-definitions';
import { safeJsonParse } from '@youzan/order-domain-utils';
import { div } from '@youzan/retail-utils';

import { SEND_TYPE_EXTRA_MAP } from '../express-extra';
import { getExpressPrinter, EXPRESS_LOCAL_STORAGE_KEY } from '../../storage';
import { defaultState } from '../../default';
import {
  EXPRESS_SYSTEM_CALL,
  SF_EXPRESS_CODE,
  JD_EXPRESS_CODE,
  NO_EXPRESS,
  THIRD_LOCAL_DELIVERY
} from '../../constants';
import { WaybillVersionEnum, PaymentTypeEnum } from '../../types';
import { getPrinters } from '../../../cainiao-printer/printer';

import * as api from '../../api';

/**
 * 电子面单表单组件逻辑抽离，可单独使用
 *  */
class ExpressExtraComponent extends React.Component {
  timeout = null;

  constructor(props) {
    super(props);
  }
  state = {
    // 发货方式
    expressType: get(this.props, 'data.supportDeliveryTypes[0].id'),

    // 电子面单签约的物流公司
    systemCallExpressCompanies: [],

    // 打印机列表
    printerList: [],

    selectedItemIds: [],

    // 是否同意协议
    isAgreeProtocol: false,

    // 是否可以发货
    deliveryDisabled: true,

    ...cloneDeep(defaultState),

    time: '',

    fetching: false,

    exceptionInfo: null,

    // 专人直送
    isDirectDelivery: false,

    deliveryTool: 0, // 交通工具
    isSystemCallExpressCompaniesMap: {}
  };

  componentDidUpdate(prevProps, prevState) {
    // 当 express 发生变化时，通知父组件
    if (prevState.express !== this.state.express) {
      if (this.props.onExpressChange) {
        const express = this.handleExpressExtra(this.state.express || {});
        this.checkFormValid(express);
      }
    }
  }

  handleExpressExtra = (express) => {
    const { waybillVersion } = this.props;
    const { systemCallExpressCompanies = [] } = this.state;
    const pickUpStartTime = div(+(express.startTime || 0), 1000);
    const pickUpEndTime = pickUpStartTime + 3600;
    const isNewWayBill = waybillVersion === WaybillVersionEnum.New;
    if (isNewWayBill) {
      return this.expressHelper.getExpressPostData(express);
    } else {
      express.startTime = express.expressId === SF_EXPRESS_CODE ? pickUpStartTime : '';
      express.endTime = express.expressId === SF_EXPRESS_CODE ? pickUpEndTime : '';
    }
    if (isNil(express.paymentType)) {
      express.paymentType = systemCallExpressCompanies.find(
        (i) => i.expressId === express.expressId
      )?.paymentType;
    }
    return express;
  };

  checkDepositFee = () => {
    // 2023-06-14 推荐物流项目取消保证金校验，以后端返回的depositOffline为准
    const { express } = this.state;
    return express.depositOffline;
  };

  checkExpressWayBillValid = () => {
    const { express } = this.state;
    if (express?.isPay) {
      return this.checkDepositFee();
    }
    return true;
  };

  checkDeliveryAddress = () => {
    const { express = {} } = this.state;
    const currentAddress = express.expressAddrList?.find(
      (res) => res.auditNo === (express.auditNo || express.fakeId)
    );
    const hasAbility = currentAddress?.hasAbility;
    if (hasAbility === false && currentAddress?.reason) {
      return false;
    }
    return true;
  };

  checkFormValid = async (express) => {
    const { zentForm } = this.props;

    // 等待表单验证完成
    zentForm.validateForm(true, () => {
      const isFormValid = zentForm.isValid();
      const isActionDisabled =
        !isFormValid ||
        !this.checkExpressWayBillValid() ||
        !this.checkDeliveryAddress() ||
        (!express.auditNo && !express.fakeId) ||
        (express.paymentType === PaymentTypeEnum.Authority &&
          express.YZShoppingInfo?.waitJoin &&
          !express.agreeProtocol) ||
        (express.paymentType === PaymentTypeEnum.Authority && express.YZShoppingInfo?.suspended);

      if (this.props.onExpressChange) {
        this.props.onExpressChange({ ...express, isActionDisabled });
      }
    });
  };

  // 获取多网点id
  getStoreId = () => {
    const { storeId = 0 } = this.props;

    if (isRetailSingleStore && get(window, '_global.business.isShowMultiStore', false)) {
      return storeId;
    }

    return null;
  };

  // 获取计算运费接口的参数
  getCaclPostageParams = (type) => {
    const { distWeight = 0 } = this.state.localDelivery;
    let { channel } = this.state.localDelivery;
    const { weight, expressId, auditNo } = this.state.express;
    const { isDirectDelivery, canDirectDelivery, expressType } = this.state;
    const { orderNo, data = {} } = this.props;
    const { channels = [] } = data;

    // 如果没有设置过 channel ，使用在 当前 channels 的默认值
    if (!channel?.deliveryChannel) {
      let PreLocalStorageChannel = window.localStorage.getItem(EXPRESS_CHANNEL_KEY_NAME);
      PreLocalStorageChannel = PreLocalStorageChannel ? Number(PreLocalStorageChannel) : '';
      if (PreLocalStorageChannel !== 9 && PreLocalStorageChannel) {
        channel = {};
        channel.deliveryChannel = PreLocalStorageChannel;
      }
    }
    const { initialWeight = 1000, supportInsurance } = find(channels, channel) || {};

    const deliveryPointId = isRetailSingleStore ? KDT_ID : data.warehouseId;

    const distChannel = channel.deliveryChannel;

    const params =
      type === 'EXPRESS'
        ? {
            distWeight: weight * 1000,
            orderNo,
            deliveryType: 14,
            expressId,
            auditNo
          }
        : {
            distWeight: distWeight * 1000 || initialWeight,
            orderNo,
            deliveryType: 21, // (目前只有同城配送会计算运费，先写死)
            distChannel,
            appId: channel.appId,
            warehouseId: deliveryPointId,
            deliveryTool: expressType === THIRD_LOCAL_DELIVERY ? this.state.deliveryTool : 0
            // deliveryPointId
          };

    const storeId = this.getStoreId();

    if (storeId) {
      params.storeId = storeId;
    }

    // 如果该服务商支持保价，则传递保价的参数
    if (supportInsurance) {
      const {
        insuranceAmount = 0,
        insuranceProduct,
        insuranceProductDesc = '不保价'
      } = this.state.insurance || {};
      params.insuranceAmount = insuranceAmount;
      params.insuranceProduct = insuranceProduct;
      params.insuranceProductDesc = insuranceProductDesc;
    }

    // 是否支持专人直送
    if (canDirectDelivery) {
      params.isDirectDelivery = isDirectDelivery;
    }

    params.itemIdList = this.state.selectedItemIds;
    return params;
  };

  // 电子面单禁用的情况
  checkSystemExpressDisable = (expressType, isAgreeProtocol) => {
    const errorDesc = get(
      this.props,
      'data.electronicSheetExceptionInfo.electronicSheetExceptionDesc'
    );
    const agreeProtocol = isNil(isAgreeProtocol) ? this.state.isAgreeProtocol : isAgreeProtocol;

    const { expressId, isCashPledgeEnough, configErrorDesc, weight } = this.state.express;

    if (expressType !== EXPRESS_SYSTEM_CALL) {
      return false;
    }

    if (expressId === SF_EXPRESS_CODE) {
      return !(agreeProtocol && !errorDesc);
    }

    if (expressId === JD_EXPRESS_CODE) {
      return !!(!isCashPledgeEnough || configErrorDesc || !weight);
    }

    return !expressId || errorDesc;
  };

  // 计算上门取件运费
  calcExpressPostage = () => {
    const params = this.getCaclPostageParams('EXPRESS');
    const { express, expressType } = this.state;

    if (!params.auditNo || !params.distWeight) {
      this.setState({ fetching: false });
      return;
    }

    api
      .calcExpressPostage(params)
      .then(({ fee, electronicSheetExceptionInfo = {}, recommendationDesc }) => {
        const isCashPledgeEnough =
          electronicSheetExceptionInfo.electronicSheetExceptionCode !== NOT_SUFFICIENT_FUNDS_CODE;
        this.setState(
          {
            express: {
              ...express,
              postage: fee,
              isCashPledgeEnough,
              recommendationDesc
            }
          },
          () => {
            const deliveryDisabled = this.checkSystemExpressDisable(expressType);
            this.setState({ deliveryDisabled });
          }
        );
      })
      .catch((err) => {
        Notify.error(err.msg || '运费计算失败！');
      })
      .finally(() => {
        this.setState({ fetching: false });
      });
  };

  @debounce(500)
  calcExpressPostageDeb() {
    this.calcExpressPostage();
  }

  // 物流的重量修改
  handleExpressWeightChange = (value) => {
    const { express } = this.state;

    if (value === express.weight) {
      return;
    }

    this.setState(
      {
        fetching: true,
        express: {
          ...express,
          weight: value
        }
      },
      this.calcExpressPostageDeb
    );
  };

  // 电子面单物流公司修改(触发联动)
  onSystemExpressCompanyChange = ({ target: { value: expressId } }) => {
    const { express } = this.state;
    this.setState(
      {
        express: {
          ...express,
          expressId,
          weight: expressId === JD_EXPRESS_CODE ? express.weight : '',
          postage: expressId === JD_EXPRESS_CODE ? express.postage : ''
        }
      },
      () => this.fetchExpressAddressList()
    );
  };

  handleGetOldWayBillAddress = async (params, options) => {
    const { express, expressType } = this.state;
    await api
      .fetchExpressConfig(params)
      .then(
        ({
          deliveryAddress = [],
          deliveryTypeList = [],
          electronicSheetExceptionInfo = {},
          ...data
        }) => {
          const expressAddrList = deliveryAddress.map(
            ({
              auditNo = '',
              address = '',
              cityName = '',
              countyName = '',
              provinceName = ''
            }) => ({
              auditNo,
              displayAddress: `${provinceName}${cityName} ${countyName} ${address}`
            })
          );

          const sendType = get(deliveryTypeList, '[0].code');
          const auditNo = get(options, 'auditNo') || get(deliveryAddress, '[0].auditNo', '');

          const newState = {
            express: {
              ...express,
              expressAddrList,
              auditNo,
              sendTypes: deliveryTypeList,
              sendType,
              configErrorCode: '',
              configErrorDesc: ''
            }
          };

          switch (express.expressId) {
            case JD_EXPRESS_CODE:
              {
                const lastId = safeJsonParse(
                  localStorage.getItem(StorageKeyJdExpressLastEWaybillTemplateId)
                );
                if (lastId) {
                  newState.express.eWaybillTemplateId = lastId;
                }
                newState.express.eWaybillTemplateOptions =
                  data.expressWaybillSizeTypes?.map((item) => {
                    return {
                      id: item.templateUrl,
                      name: item.specification
                    };
                  }) || [];
              }

              break;

            default:
              break;
          }

          if (!isEmpty(electronicSheetExceptionInfo)) {
            newState.express.configErrorCode =
              electronicSheetExceptionInfo.electronicSheetExceptionCode;
            newState.express.configErrorDesc =
              electronicSheetExceptionInfo.electronicSheetExceptionDesc;
          }

          newState.deliveryDisabled = this.checkSystemExpressDisable(expressType);
          this.setState(newState);
        }
      )
      .catch((err) => {
        const msg = err.msg || '发货地址列表数据获取失败';
        Notify.error(msg);
        throw Error(msg);
      });
  };

  handleGetNewWayBillAddress = async (params) => {
    const isAuthority = params.paymentType === PaymentTypeEnum.Authority;
    params.retailKdtId = this.props.currentElectronWayBillServiceKdtId;
    return await api[!isAuthority ? 'getDeliveryAddresses' : 'getAuthorityDeliveryAddresses'](
      params
    ).then((data = []) => {
      let addressList = data;
      addressList = addressList.map((item) => {
        const displayAddress = `${item.provinceName} ${item.cityName} ${item.countyName} ${
          item.address
        } ${item.contactName ? item.contactName : ''} ${
          item.mobile || item.phone ? item.mobile || item.phone : ''
        }`;
        return {
          ...item,
          auditNo: item.id || item.auditNo,
          displayAddress
        };
      });
      const newState = {
        express: {
          ...this.state.express,
          expressAddrList: addressList
        }
      };
      this.setState(newState);
      return addressList;
    });
  };

  /**
   * 获取发货地址列表
   * @params {Object} options
   */
  fetchExpressAddressList = async (options, brandCode = '') => {
    const { express, expressType } = this.state;
    const { waybillVersion } = this.props;
    const isNewWayBill = waybillVersion === WaybillVersionEnum.New;

    const { expressId, paymentType } = express;
    // 新电子面单顺丰不请求地址，因为还需要依赖品牌的选择
    if (
      !expressId ||
      expressType === NO_EXPRESS ||
      (isNewWayBill && expressId === SF_EXPRESS_CODE)
    ) {
      this.setState({
        express: {
          ...express,
          expressAddrList: []
        }
      });
      return;
    }

    if (waybillVersion === WaybillVersionEnum.Old) {
      await this.handleGetOldWayBillAddress({ expressId, deliveryWay: 1 }, options);
    } else if (waybillVersion === WaybillVersionEnum.New) {
      await this.handleGetNewWayBillAddress({
        expressId,
        paymentType,
        waybillVersion,
        brandCode
      });
    }
  };

  // 获取面单量情况
  fetchExpressSheetNum = () => {
    let { deliveryDisabled } = this.state;
    const {
      express: { expressId, auditNo }
    } = this.state;
    api
      .fetchExpressSheetNum({ expressId, auditNo })
      .then(({ remainNum, isLimit }) => {
        let printerErrDesc = '';

        if (isLimit && remainNum < 1) {
          printerErrDesc = '面单余量不足，请联系网点充值。';
          deliveryDisabled = true;
        }

        this.setState({
          printerErrDesc,
          deliveryDisabled
        });
      })
      .catch((err) => Notify.error(err.msg || '获取面单余量情况失败！'));
  };

  // 发货地址修改
  onExpressAddressChange = ({ target: { value: auditNo } }) => {
    const { express } = this.state;
    if (!auditNo) {
      this.setState({ express: { ...express, auditNo: '' } });
      return;
    }
    this.setState({ express: { ...express, auditNo } }, () => {
      this.fetchExpressSheetNum();
      // 如果重量不为空，重新计算下运费
      if (express.weight) {
        this.calcExpressPostage();
      }
    });
  };

  // 打印机修改
  onPrinterChange = ({ target: { value: printerDeviceNo } }) => {
    const { express, printerList } = this.state;
    const printerInfo = find(printerList, { equipmentNumber: express?.printerDeviceNo }) || {};
    this.setState({ express: { ...express, printerDeviceNo, printerInfo } });
  };

  // 上门取件时间修改
  handlePickUpTimeChange = (startTime) => {
    const { express } = this.state;
    this.setState({
      express: {
        ...express,
        startTime
      }
    });
  };

  // 电子面单下的发货方式修改
  onSendTypeChange = ({ target: { value } }) => {
    const { express } = this.state;
    this.setState({
      express: {
        ...express,
        sendType: value
      }
    });
  };

  onChangeExpress = (newExpress) => {
    this.setState((state) => {
      return {
        express: {
          ...state.express,
          ...newExpress
        }
      };
    });
  };

  handleExpressValueChange = (value, isCover = false) => {
    let params = {
      ...this.state.express,
      ...value
    };
    if (isCover) {
      params = {
        ...value
      };
    }
    this.setState(() => {
      return {
        express: params
      };
    });
    this.forceUpdate();
  };

  // 电子面单排序
  sortSystemCallExpressCompanies = async (express, systemCallExpressCompanies) => {
    const { waybillVersion, currentElectronWayBillServiceKdtId } = this.props;
    let newSystemCallExpressCompanies = [...systemCallExpressCompanies];
    // 新电子面单之前使用过的服务商展示在最前面逻辑,并默认选中
    if (waybillVersion === WaybillVersionEnum.New) {
      let defaultExpressId = '';
      let defaultPaymentType = '';
      let defaultExpress;
      let maxExpress = {};
      const defaultExpressKey =
        window.localStorage.getItem(
          `${EXPRESS_LOCAL_STORAGE_KEY}_${currentElectronWayBillServiceKdtId}`
        ) || '';
      if (defaultExpressKey) {
        [defaultExpressId, defaultPaymentType] = defaultExpressKey.split('_');
        defaultExpress = newSystemCallExpressCompanies.find(
          (item) =>
            item.expressId === +defaultExpressId &&
            item.paymentType === +defaultPaymentType &&
            item?.channelStatus !== 2
        );
        maxExpress = newSystemCallExpressCompanies.sort(
          (pre, next) => next.recommendPriority - pre.recommendPriority
        )[0];
      }

      newSystemCallExpressCompanies = newSystemCallExpressCompanies
        .map((item) => ({
          ...item,
          recommendPriority:
            item.expressId === +defaultExpressId && item.paymentType === +defaultPaymentType
              ? (maxExpress.recommendPriority || 0) + 1
              : item.recommendPriority
        }))
        .sort((pre, next) => next.recommendPriority - pre.recommendPriority);

      const defaultSelectedExpress = defaultExpress || newSystemCallExpressCompanies[0];
      if (
        defaultSelectedExpress &&
        !this.state.isSystemCallExpressCompaniesMap[currentElectronWayBillServiceKdtId]
      ) {
        this.handleExpressValueChange({
          ...defaultSelectedExpress,
          sendType: defaultSelectedExpress.type,
          weight:
            defaultSelectedExpress.paymentType === PaymentTypeEnum.Authority ? 1000 : undefined // 有赞寄件重量默认 1kg
        });

        // 电子面单的时候才请求收货地址
        await this.fetchExpressAddressList({ auditNo: defaultSelectedExpress.auditNo });
        this.setState(
          {
            isSystemCallExpressCompaniesMap: {
              [currentElectronWayBillServiceKdtId]: true
            },
            systemCallExpressCompanies: newSystemCallExpressCompanies
          }
        );
      } else {
        this.setState({
          systemCallExpressCompanies: newSystemCallExpressCompanies
        });
      }
    } else {
      newSystemCallExpressCompanies = systemCallExpressCompanies.sort(
        (pre, next) => next.recommendPriority - pre.recommendPriority
      );
      if (express.historyExpressId && newSystemCallExpressCompanies?.length > 0) {
        const max = newSystemCallExpressCompanies[0];
        newSystemCallExpressCompanies = newSystemCallExpressCompanies
          .map((item) => ({
            ...item,
            recommendPriority:
              item.expressId === express.historyExpressId
                ? (max.recommendPriority || 0) + 1
                : item.recommendPriority
          }))
          .sort((pre, next) => next.recommendPriority - pre.recommendPriority);
      }
      this.setState({ systemCallExpressCompanies: newSystemCallExpressCompanies });
    }

    return newSystemCallExpressCompanies;
  };

  // 获取电子面单物流公司列表
  fetchElectronCompanies = (isNewWayBill, currentElectronWayBillServiceKdtId) => {
    const apiFn = isNewWayBill ? api.getExpressByNewWayBill : api.fetchSystemCallExpressCompanies;
    let params = {};
    if (isNewWayBill) {
      const { auditNo = '', weight = '' } = this.state.express;
      params = {
        auditNo,
        weight,
        orderNo: this.props.orderNo,
        retailKdtId: currentElectronWayBillServiceKdtId
      };
    }
    return apiFn(params)
      .then((res = []) => {
        let systemCallExpressCompanies = res;
        // 连锁店铺电子面单屏蔽京东物流
        if (!isNewWayBill && !isRetailSingleStore) {
          systemCallExpressCompanies = systemCallExpressCompanies.filter(
            ({ expressId }) => expressId !== JD_EXPRESS_CODE
          );
        }

        this.sortSystemCallExpressCompanies(this.state.express, systemCallExpressCompanies);
      })
      .catch((err) => {
        Notify.error(err.msg || '获取物流公司列表失败！');
      });
  };

  updateDeliveryExpress = () => {
    const { waybillVersion, currentElectronWayBillServiceKdtId } = this.props;
    // 请求需要等setState生效，因此延迟一下
    setTimeout(() => {
      this.fetchElectronCompanies(
        waybillVersion === WaybillVersionEnum.New,
        currentElectronWayBillServiceKdtId
      );
    }, 50);
  };

  // 获取打印机列表
  fetchPrinterList = (isNewWayBill) => {
    // 新电子面单-获取菜鸟打印机
    if (isNewWayBill) {
      getPrinters().then((printers = []) => {
        this.setState({
          printerList: printers
        });
        const printerDeviceNo = getExpressPrinter(this.props.currentElectronWayBillServiceKdtId);
        if (printerDeviceNo && printers.find((item) => item.id === printerDeviceNo)) {
          this.setState({
            express: {
              ...this.state.express,
              printerDeviceNo
            }
          });
        }
      });
    } else {
      api
        .fetchPrinterList()
        .then(({ items = [] }) => {
          this.setState({ printerList: items });
        })
        .catch((err) => {
          Notify.error(err.msg || '获取打印机列表失败！');
        });
    }
  };

  handleExpressCompaniesChange = (newSystemCallExpressCompanies) => {
    this.setState({ systemCallExpressCompanies: newSystemCallExpressCompanies });
  };
  handleGenerateHelper = (helper) => {
    this.expressHelper = helper;
  };

  getExtraProps = () => {
    const {
      handleExpressWeightChange,
      onSystemExpressCompanyChange,
      onExpressAddressChange,
      onPrinterChange,
      handlePickUpTimeChange,
      onSendTypeChange,
      onChangeExpress,
      fetchElectronCompanies,
      updateDeliveryExpress,
      handleExpressValueChange,
      handleGetNewWayBillAddress,
      fetchPrinterList,
      handleExpressCompaniesChange,
      handleGenerateHelper
    } = this;

    const {
      orderInfo,
      channelType,
      placeType,
      orderNo,
      waybillVersion,
      currentElectronWayBillServiceKdtId,
      currentElectronWayBillServiceKdtName,
      isEditExpress
    } = this.props;
    const { electronicSheetExceptionInfo = {} } = this.props.data;
    const baseProps = {
      ...this.state
    };

    const isNewWayBill = waybillVersion === WaybillVersionEnum.New;

    if (isNewWayBill) {
      return {
        ...baseProps,
        waybillVersion: 2,
        currentElectronWayBillServiceKdtId,
        currentElectronWayBillServiceKdtName,
        orderNo,
        onExpressValueChange: handleExpressValueChange,
        onExpressCompaniesChange: handleExpressCompaniesChange,
        generateHelper: handleGenerateHelper
      };
    }

    return {
      ...baseProps,
      ...electronicSheetExceptionInfo,
      orderInfo,
      channelType,
      onSystemExpressCompanyChange,
      onExpressAddressChange,
      onPrinterChange,
      handleExpressWeightChange,
      handlePickUpTimeChange,
      onSendTypeChange,
      onChangeExpress,
      placeType,
      waybillVersion,
      fetchElectronCompanies: fetchElectronCompanies,
      currentElectronWayBillServiceKdtId,
      currentElectronWayBillServiceKdtName,
      updateDeliveryExpress,
      onExpressValueChange: handleExpressValueChange,
      onGetNewWayBillAddress: handleGetNewWayBillAddress,
      fetchPrinterList,
      orderNo,
      isEditExpress
    };
  };

  componentDidMount() {
    const { data, waybillVersion, currentElectronWayBillServiceKdtId } = this.props;
    const isNewWayBill = waybillVersion === WaybillVersionEnum.New;
    // 在线下单
    if (
      get(data, 'supportDeliveryTypes', []).some((type) => type.id === EXPRESS_SYSTEM_CALL) &&
      !isNewWayBill
    ) {
      this.fetchPrinterList(isNewWayBill);
      this.fetchElectronCompanies(isNewWayBill, currentElectronWayBillServiceKdtId);
    }
  }

  render() {
    const ExtraComponent = SEND_TYPE_EXTRA_MAP[EXPRESS_SYSTEM_CALL];
    return <ExtraComponent {...this.getExtraProps()} />;
  }
}

export default ExpressExtraComponent;

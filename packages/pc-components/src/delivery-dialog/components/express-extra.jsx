import { BlankLink } from '@youzan/react-components';
import { useLocalStorage } from '@youzan/react-hooks';
import { PopInfo } from '@youzan/retail-components';
import { div, formatDate, global, setUrlDomain } from '@youzan/retail-utils';
import { isRetailSingleStore } from '@youzan/utils-shop';
import { Select } from '@zent/compat';
import { find, get } from 'lodash';
import { set } from 'lodash/fp';
import React, { useEffect, useState } from 'react';
import { FormDescription, Input, Notify, Radio, Icon, Pop } from 'zent';

import { SupportParallelCallInLocalDelivery } from '../express-content';
import { AuditStatus, DeliveryChannel, WaybillVersionEnum } from '../types';
import CallType, { ParallelOrSequenceDeliveryCallType } from './call-type';
import DeliveryTool from './delivery-tool';
import ExpressSelfInputs from './express-self-inputs';
import ExpressWxInputs from './express-wx-inputs';
import ExpressExtraSystemCallCommon from './express-extra-system-call';

import { queryAppLink, searchWechatDeliveryConfig } from '../api';
import { SEND_TYPE_CODE } from '../constants';
import { DeliveryService } from './direct-service';

import { ExpressExtraSystemCallOld } from './express-extra-system-call-old';
import DeliveryServiceList from './delivery-service-list';

const { KDT_ID } = global;

// eslint-disable-next-line @youzan/yz-retail/no-new-date
const specialBusinessType = new Date(2022, 6, 9, 0, 0, 0).valueOf() - new Date().valueOf() > 0;

const RadioGroup = Radio.Group;

// 在线电子面单发货后续只迭代ExpressExtraSystemCallCommon组件
export const ExpressExtraSystemCall = (props) => {
  const { waybillVersion } = props;
  const isNewWayBill = waybillVersion === WaybillVersionEnum.New;
  if (!isNewWayBill) {
    return <ExpressExtraSystemCallOld {...props} />;
  }
  return <ExpressExtraSystemCallCommon {...props} />;
};

// 物流公司发货时需要展示的信息
export const ExpressExtraForCompany = (props) => {
  const {
    express = {},
    multiPackInfo = [],
    onChange,
    isMultiExpress,
    disableMulti,
    disableMultiDesc
  } = props;

  const onExpressChange = (packs) => {
    if (isMultiExpress) {
      onChange({ multiPackInfo: packs });
      return;
    }
    onChange({ express: packs[0] || {} });
  };

  const value = isMultiExpress ? multiPackInfo : [express];

  return (
    <ExpressSelfInputs
      onChange={onExpressChange}
      value={value}
      disableMulti={disableMulti}
      disableMultiDesc={disableMultiDesc}
      multiExpress={isMultiExpress}
    />
  );
};

// 微信物流发货时需要的extra信息
export const ExpressExtraForWxExpress = (props) => {
  const { onChange } = props;

  const [availableWechatExpress, setAvailableWechatExpress] = useState([]);

  useEffect(() => {
    searchWechatDeliveryConfig({
      includePrinterInfo: false,
      includeAllSupportDeliveryAddress: false
    })
      .then((result) => {
        if (result) {
          const expresses = result.wechatDeliveryExpressAggDTOS || [];
          const wechatExpress = expresses.filter((express) => {
            const accouts = express.deliveryBindAccountDTOS || [];
            return accouts.some((v) => v.bindStatusCode === 0); // BindStatus.Success: 0
          });
          setAvailableWechatExpress(wechatExpress);
        }
      })
      .catch((err) => {
        Notify.error(err);
      });
  }, []);

  return (
    <ExpressWxInputs
      wechatExpressWayBill={{}}
      availableWechatExpress={availableWechatExpress}
      onChange={onChange}
    />
  );
};

// 同城配送第三方配送，需要展示第三配送公司以及配送信息
export const ThirdDeliveryExtra = ({
  localPostage,
  calcWeightType,
  localDelivery,
  channels = [],
  handleDeliveryCompanyChange,
  localChannels = [],
  handleWeightInputTypeChange,
  handleWeightChange,
  exceptionInfo,
  autoCallInfo = {},
  supportInsurance,
  insuranceList,
  insurance,
  handleInsuranceChange,
  warehouseId,
  businessType,
  localDeliveryCallConfig,
  handleDeliveryChannelChange,
  handleRefresh,
  discountFee,
  benefitSubsidyFee,
  canDirectDelivery = false,
  isDirectDelivery,
  handleDirectDeliveryChange,
  refreshDeliveryChannel,
  expressType,
  isShowDeliveryTool,
  handleChangeDeliveryTool,
  deliveryTool
}) => {
  const [appLink, setAppLink] = useState('');

  const settingUrl = isRetailSingleStore
    ? `${window._global.url.store}/shop/v2/trade/localdelivery`
    : `${window._global.url.store}/setting/common/order`;
  const { channel = {}, distWeight } = localDelivery;
  const [deliveryPriceSpecialBusinessTip, setDeliveryPriceSpecialBusinessTip] = useLocalStorage(
    'delivery-price-special-business-tip'
  );

  useEffect(() => {
    if (channel.appId && exceptionInfo && exceptionInfo.electronicSheetExceptionCode) {
      queryAppLink(channel.appId).then((link) => setAppLink(link));
    }
  }, [exceptionInfo, channel.appId]);

  useEffect(() => {
    if (!isShowDeliveryTool) {
      handleChangeDeliveryTool(0);
    }
  }, [handleChangeDeliveryTool, isShowDeliveryTool]);

  if (channels.length === 0) {
    return (
      <div className="delivery-extra">
        <p>
          暂未启用第三方同城配送服务
          <BlankLink href={settingUrl}>前往开启</BlankLink>
        </p>
      </div>
    );
  }

  const { initialWeight = 1000, maxWeight } = find(channels, channel) || {};

  const onWeightChange = (evt) => {
    const {
      target: { value }
    } = evt;
    const weight = value * 1000 > maxWeight ? maxWeight / 1000 : value;
    handleWeightChange(set('target.value', weight)(evt));
  };

  const { isShow, autoDeliveryTime } = autoCallInfo;
  const now = new Date();

  const isNotParallelOrSequenceDeliveryCallType = !ParallelOrSequenceDeliveryCallType.includes(
    channel?.deliveryChannel
  );

  const deliveryChannels = localChannels.filter(
    (item) =>
      item.status === AuditStatus.NoAudit ||
      item.status === AuditStatus.Pending ||
      item.status === AuditStatus.Success
  );

  // 获取当前的 channel 名称
  const currentChannelName = get(
    deliveryChannels.find(
      (c) =>
        get(c, 'value.deliveryChannel') === channel.deliveryChannel &&
        get(c, 'value.appId') === channel.appId
    ),
    'rawText',
    ''
  );

  return (
    <div className="delivery-extra">
      <div className="delivery-extra-content">
        {SupportParallelCallInLocalDelivery && (
          <CallType
            value={channel}
            warehouseId={warehouseId}
            businessType={businessType}
            localDeliveryCallConfig={localDeliveryCallConfig}
            onChange={handleDeliveryChannelChange}
            onRefresh={handleRefresh}
            expressType={expressType}
          />
        )}
        {/* 非同时呼叫和非顺序呼叫时，才能选择单个配送服务商 */}
        {isNotParallelOrSequenceDeliveryCallType && (
          <div className="input-item" style={{ flexWrap: 'wrap' }}>
            <div className="delivery-extra-content__label">配送公司：</div>
            <DeliveryServiceList
              value={channel}
              data={deliveryChannels}
              onChannelSelect={handleDeliveryCompanyChange}
              onSelect={handleDeliveryCompanyChange}
              refreshDeliveryChannel={refreshDeliveryChannel}
            />
          </div>
        )}
        {canDirectDelivery && (
          <DeliveryService
            isDirectDelivery={isDirectDelivery}
            onChange={handleDirectDeliveryChange}
          />
        )}

        {isShowDeliveryTool && (
          <DeliveryTool onChange={handleChangeDeliveryTool} value={deliveryTool} />
        )}
        <div className="input-item dist-Weight">
          <div className="delivery-extra-content__label">商品重量：</div>
          <div className="delivery-extra-content__value">
            <RadioGroup onChange={handleWeightInputTypeChange} value={calcWeightType}>
              <Radio value="default">
                {div(initialWeight, 1000)}
                千克以内
              </Radio>
              <Radio value="custom">
                <Input
                  className="delivery-custom-weight"
                  disabled={calcWeightType !== 'custom'}
                  onChange={onWeightChange}
                  value={distWeight}
                />
                <span>千克</span>
              </Radio>
            </RadioGroup>
            <PopInfo
              className="delivery-extra-content__value__pop-info"
              popContent="默认重量值为网店商品重量之和，若没有填写重量，重量会当作 0 进行计算。"
            />
          </div>
        </div>

        {isNotParallelOrSequenceDeliveryCallType && supportInsurance && (
          <div className="input-item">
            <div className="delivery-extra-content__label">商品保价：</div>
            <div className="delivery-extra-content__value">
              <Select
                className="delivery-baojia"
                placeholder="请选择保价"
                value={insurance}
                onChange={(value) => {
                  handleInsuranceChange(value);
                }}
                data={insuranceList}
                autoWidth
              />
            </div>
          </div>
        )}

        <div className="delivery-price">
          <div className="delivery-extra-content__label">配送价格：</div>
          <div>
            <div className="delivery-extra-content__value_1">
              <span>{localPostage}</span>
              {discountFee && (
                <span className="error-span">{`已优惠${div(discountFee, 100)}元`}</span>
              )}
              {benefitSubsidyFee && (
                <>
                  <span className="error-span">{`有赞再补贴${div(benefitSubsidyFee, 100)}元`}</span>
                  <Pop
                    style={{ width: '300px' }}
                    trigger="hover"
                    position="right-center"
                    content={`开店礼包专属权益，限时享每单补贴${div(
                      benefitSubsidyFee,
                      100
                    )}元，以配送费结算时计数为准。详情见有赞同城配服务主页。`}
                  >
                    <Icon
                      style={{
                        color: '#c8c9cc',
                        fontSize: '18px',
                        marginLeft: '3px',
                        display: 'flex',
                        alignItems: 'center'
                      }}
                      type="help-circle"
                    />
                  </Pop>
                </>
              )}
            </div>
            <div className="delivery-extra-content__value">
              {channel.deliveryChannel === DeliveryChannel.CanDao && (
                <p className="tips">餐道聚合配送将在8月10日0点下线，可开通呼叫其他配送服务商</p>
              )}
              {/* 第三方配送余额不足的时候提示 */}
              {exceptionInfo && channel.appId && exceptionInfo.electronicSheetExceptionDesc ? (
                <p className="exception">
                  {`你在${currentChannelName}的${exceptionInfo.electronicSheetExceptionDesc}，`}
                  {exceptionInfo.electronicSheetExceptionCode === 233116004 ? (
                    <BlankLink href={appLink}>前往充值</BlankLink>
                  ) : null}
                  {exceptionInfo.electronicSheetExceptionCode === 233116005
                    ? `请联系${currentChannelName}了解账户异常详情。`
                    : null}
                </p>
              ) : null}

              {exceptionInfo && !channel.appId && exceptionInfo.electronicSheetExceptionDesc ? (
                <p className="exception">{exceptionInfo.electronicSheetExceptionDesc}</p>
              ) : null}
            </div>

            {!deliveryPriceSpecialBusinessTip && specialBusinessType && (
              <FormDescription>
                特殊业务类型支持商家按照商品分类呼叫服务商，节省配送费用。
                <BlankLink
                  href={setUrlDomain(`/fulfillment/delivery#/local-delivery/${KDT_ID}`, 'store')}
                >
                  去设置{' '}
                </BlankLink>
                <a onClick={() => setDeliveryPriceSpecialBusinessTip(true)}>| 不再提醒 </a>
              </FormDescription>
            )}
          </div>
        </div>
      </div>
      {SupportParallelCallInLocalDelivery && isShow && (
        <div className="delivery-extra-autoCall">{autoCallInfo.autoDeliveryDesc}</div>
      )}
      {!SupportParallelCallInLocalDelivery && isShow && autoDeliveryTime - +now > 0 && (
        <div className="delivery-extra-autoCall">
          {`已开启自动呼叫，系统将在 ${formatDate(
            autoDeliveryTime,
            'YYYY-MM-DD HH:mm'
          )} 自动呼叫配送员`}
        </div>
      )}
    </div>
  );
};

// 自提有码核销，展示核销码输入框
export const SelfFetchExtra = ({ selfFetch = {}, onChange }) => (
  <div className="express-content-item">
    <div className="express-content-item__label" />
    <div className="express-content-item__value selffetch">
      提货码：
      <Input
        value={selfFetch.selfFetchNo}
        className="od-self-fetch-margin"
        onChange={({ target: { value: selfFetchNo } }) => {
          const reg = /^[0-9a-zA-Z]*$/;
          if (!reg.test(selfFetchNo)) return;
          onChange({
            selfFetch: {
              ...selfFetch,
              selfFetchNo: selfFetchNo.trim()
            }
          });
        }}
      />
    </div>
  </div>
);

// 无码核销，展示空
export const WithoutCodeTips = () => (
  <div className="express-content-item pl100">
    <p className="orange">请仔细核对联系人信息，避免冒领或者错误核销造成损失！</p>
  </div>
);

const {
  EXPRESS_FOR_COMPANY,
  THIRD_LOCAL_DELIVERY,
  SELFFETCH_WITH_CODE,
  SELFFETCH_WITHOUT_CODE,
  EXPRESS_SYSTEM_CALL,
  WX_EXPRESS
} = SEND_TYPE_CODE;

export const SEND_TYPE_EXTRA_MAP = {
  [EXPRESS_FOR_COMPANY]: ExpressExtraForCompany,
  [THIRD_LOCAL_DELIVERY]: ThirdDeliveryExtra,
  [SELFFETCH_WITH_CODE]: SelfFetchExtra,
  [SELFFETCH_WITHOUT_CODE]: WithoutCodeTips,
  [EXPRESS_SYSTEM_CALL]: ExpressExtraSystemCall,
  [WX_EXPRESS]: ExpressExtraForWxExpress
};

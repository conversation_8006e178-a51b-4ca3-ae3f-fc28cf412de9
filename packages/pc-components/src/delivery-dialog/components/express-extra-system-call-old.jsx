/**
 * 老的电子面单发货组件，包含了新老版本电子面单，不再迭代
 * 新版本电子面单迁移到express-extra-system-call中维护
 */
import { BlankLink } from '@youzan/react-components';
import { NumberInput } from '@youzan/retail-components';
import { setUrlDomain } from '@youzan/retail-utils';
import formatMoney from '@youzan/utils/money/format';
import { isRetailSingleStore } from '@youzan/utils-shop';
import { Select, Form } from '@zent/compat';
import { find, get, isNumber } from 'lodash';
import React, {
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
  useCallback
} from 'react';
import { Notify, Radio, Checkbox } from 'zent';
import cx from 'classnames';

import { DeliveryChannelType } from '@youzan/order-domain-definitions';
import { convertFenToYen } from '@youzan/order-domain-utils';
import { DeliveryPlaceType, WaybillVersionEnum, PaymentTypeEnum } from '../types';

import {
  getAppendServiceList,
  getExpressProductTypes,
  getExpressAbility,
  getWayBillTemplate,
  getExpressBrands,
  getDeliveryFee,
  getDeliveryBatchFee,
  queryDeliveryBizTypeByExpressAndAddress
} from '../api';
import {
  ELECTRON_ERROR_INFO,
  JD_EXPRESS_CODE,
  ST_EXPRESS_CODE,
  ONLY_DZMD,
  SF_EXPRESS_CODE,
  ELECTRON_WAYBILL_SERVICE_CLOSED,
  EXPRESS_WAY_BILL_TYPES_MAP,
  EXPRESS_WAY_BILL_TYPES,
  EXPRESS_SUPPORT_DETAIL_PICK_TIME
} from '../constants';
import TimeSelect from './time-select';
import {
  getExpressWayBillStorage,
  getWayBillTypeStorage,
  EXPRESS_LOCAL_STORAGE_KEY
} from '../storage';
import { prefixZero } from '../utils';
import { defaultExpressData } from '../default';

import { ExpressWxChannelShopEWaybill } from '../wx-channel-shop/components/express-wx-channel-shop-e-waybill';
import AppendServiceSelection from './append-service-selection';

const BASE_URL = get(window, '_global.url.store', '');

const RadioGroup = Radio.Group;
const { Option } = Select;

const ELECTRON_WAY_BILL_URL = '/v4/trade/electron-way-bill#/manage';
const SHOP_ADDRESS_CREATE_URL = '/v4/shop/setting/shop-address#/create';
const RECHARGE_URL = '/v4/assets/recharge';

const { FormSelectField, FormNumberInputField } = Form;

const priceSplitFormat = (val) => {
  let price = val;
  if (typeof price === 'number') {
    price = formatMoney(val);
  }
  if (!price) {
    return [];
  }
  const [intStr, floatStr = '00'] = price.split('.');
  return [intStr, floatStr.padEnd(2, '0')];
};

/**
 * 系统呼叫物流，电子面单显示
 *
 */
export const ExpressExtraSystemCallOld = forwardRef((props, ref) => {
  const {
    orderInfo,
    channelType,
    systemCallExpressCompanies = [],
    printerList = [],
    electronicSheetExceptionDesc,
    electronicSheetExceptionCode,
    express = {},
    onSystemExpressCompanyChange,
    onExpressAddressChange,
    onPrinterChange,
    printerErrDesc,
    handleExpressWeightChange,
    handlePickUpTimeChange,
    onSendTypeChange,
    onChangeExpress,
    placeType,
    waybillVersion,
    fetchElectronCompanies,
    currentElectronWayBillServiceKdtId,
    currentElectronWayBillServiceKdtName,
    updateDeliveryExpress,
    onExpressValueChange,
    onGetNewWayBillAddress,
    fetchPrinterList,
    orderNo,
    isEditExpress
  } = props;
  const [selectedExpress, setSelectedExpress] = useState({});
  const [appendServiceList, setAppendServiceList] = useState([]);
  const [productTypes, setProductTypes] = useState([]);
  const [expressBrands, setExpressBrands] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [expressFee, setExpressFee] = useState(0);
  const [subsidyFee, setSubsidyFee] = useState(0);
  const [pickTime, setPickTime] = useState(null);
  const [pickStart, setPickStart] = useState(null);
  const [pickDays, setPickDays] = useState(null);
  const [YZExpressFeeList, setYZExpressFeeList] = useState([]);
  const [STAllType, setSTAllType] = useState([]);

  const timer = useRef(null);
  const isNewWayBill = waybillVersion === WaybillVersionEnum.New;

  useEffect(() => {
    const currentExpress =
      systemCallExpressCompanies.find(
        (i) => i.expressId === express.expressId && i.paymentType === express.paymentType
      ) || {};

    // 申通快递 根据所选地址 是否支持免打单强制修改 allType
    if (currentExpress.expressId === ST_EXPRESS_CODE && STAllType.length) {
      currentExpress.allType = STAllType;
    }
    setSelectedExpress(currentExpress);
  }, [express.expressId, express.paymentType, systemCallExpressCompanies, STAllType]);

  // 初始化面单模板
  const fetchAndInitExpressTemplate = (expressItem) => {
    const storageData = getExpressWayBillStorage(
      currentElectronWayBillServiceKdtId,
      expressItem.expressId,
      expressItem.paymentType
    );
    // 面单模板
    getWayBillTemplate({
      expressId: expressItem.expressId,
      paymentType: expressItem.paymentType,
      brandCode: expressItem.brandCode
    }).then((data) => {
      setTemplates(data || []);
      // 如果有templateUrl缓存并且返回了此数据，则直接调用change事件
      if (
        storageData.eWaybillTemplateId &&
        data.find((item) => item.templateUrl === storageData.eWaybillTemplateId)
      ) {
        onExpressValueChange({ eWaybillTemplateId: storageData.eWaybillTemplateId });
      }
      return data;
    });
  };

  // 获取增值服务
  const fetchExpressAppendService = (expressId, addressVo) => {
    const { brandCode = '', latticePointNo = '' } = addressVo;

    getAppendServiceList({
      onlyAvailable: '1',
      expressId,
      brandCode,
      latticePointNo
    }).then((data) => {
      setAppendServiceList(data || []);
    });
  };

  // 获取产品类型
  const fetchProductTypes = (expressId, paymentType, brandCode = '') => {
    getExpressProductTypes({ expressId, brandCode }).then((data) => {
      setProductTypes(data);
      const storageData = getExpressWayBillStorage(
        currentElectronWayBillServiceKdtId,
        expressId,
        paymentType
      );

      if (data.find((item) => item.value === storageData.productCode)) {
        onExpressValueChange({ productCode: storageData.productCode });
      } else {
        onExpressValueChange({ productCode: '' });
      }
    });
  };

  const fetchExpressFee = useCallback(
    (weight, express) => {
      if (waybillVersion === WaybillVersionEnum.Old || !weight) {
        return;
      }
      const { expressId, auditNo, fakeId, expressAddrList } = express;
      const addressVo = expressAddrList.find((vo) => vo.auditNo === (auditNo || fakeId));
      if (!addressVo) return;
      const { cityName, countyName, provinceName, address, mobile = '', phone = '' } = addressVo;
      getDeliveryFee({
        distWeight: weight,
        orderNo,
        expressId,
        deliveryType: 14,
        province: provinceName,
        city: cityName,
        county: countyName,
        address,
        mobile,
        phone
      })
        .then(({ fee, benefitSubsidyFee = 0 }) => {
          onExpressValueChange({
            postage: fee
          });
          setExpressFee(fee);
          setSubsidyFee(benefitSubsidyFee);
        })
        .catch((err) => {
          Notify.error(err || '获取运费失败');
        });

      getDeliveryBatchFee({
        orderNo,
        weight,
        expressIds: systemCallExpressCompanies
          .filter((res) => res.paymentType === PaymentTypeEnum.Authority)
          .map((res) => res.expressId),
        sendProvince: provinceName,
        sendCity: cityName
      }).then((res) => {
        setYZExpressFeeList(res);
      });
    },
    [onExpressValueChange, orderNo, systemCallExpressCompanies, waybillVersion]
  );

  const hasAbilityCb = (val, currentExpress) => {
    if (timer.current) {
      clearTimeout(timer.current);
    }
    timer.current = window.setTimeout(() => {
      fetchExpressFee(currentExpress.weight, {
        ...currentExpress,
        // 有赞寄件场景下不能传 auditNo
        auditNo: currentExpress.paymentType === PaymentTypeEnum.Authority ? '' : val,
        fakeId: currentExpress.paymentType === PaymentTypeEnum.Authority ? val : '',
        productCode: ''
      });
    }, 500);
  };

  // 校验快递地址可达性
  const fetchExpressAbility = (val, currentExpress) => {
    const { expressAddrList, expressId } = currentExpress;
    const addressVo = expressAddrList.find((vo) => vo.auditNo === val);
    if (!addressVo) {
      return;
    }
    const { address, cityName, countyName, provinceName } = addressVo;
    return getExpressAbility({
      address,
      cityName,
      countyName,
      provinceName,
      expressId
    })
      .then((res) => {
        const { hasAbility = true, reason = '当前发货地址不在快递公司揽收范围内' } = res;
        const newAddresses = Array.from(expressAddrList);
        const currentAddress = newAddresses.find((vo) => vo.auditNo === val);
        currentAddress.hasAbility = hasAbility;
        currentAddress.reason = reason;
        onExpressValueChange({
          expressAddrList: newAddresses
        });
        return res;
      })
      .catch((err) => {
        console.log(err);
      });
  };

  // 地址变更
  const handleExpressAddressChange = (val, currentExpress = express) => {
    const { paymentType, expressId, expressAddrList } = currentExpress;

    const addressVo = expressAddrList.find((vo) => vo.auditNo === val);
    if (addressVo) {
      const { address, cityName, countyName, mobile, provinceName, phone, contactName } = addressVo;
      const addressInfo = {
        address,
        city: cityName,
        county: countyName,
        mobile,
        province: provinceName,
        phone,
        senderName: contactName
      };
      onExpressValueChange({
        // 有赞寄件场景下不能传 auditNo
        auditNo: paymentType === PaymentTypeEnum.Authority ? '' : val,
        fakeId: paymentType === PaymentTypeEnum.Authority ? val : '',
        productCode: '',
        expressWayBillAddress: addressInfo
      });
    } else {
      onExpressValueChange({
        auditNo: '',
        fakeId: ''
      });
    }

    // 非官方寄件，需要获取增值服务和顺丰的产品类型
    if (val && paymentType !== PaymentTypeEnum.Authority) {
      if (!addressVo) {
        return;
      }

      // 获取增值服务
      fetchExpressAppendService(expressId, addressVo);
      // 顺丰的产品类型
      if (expressId === SF_EXPRESS_CODE) {
        fetchProductTypes(expressId, paymentType, addressVo.brandCode || '1');
      }
    }
    if (timer.current) {
      clearTimeout(timer.current);
    }

    // 有赞寄件，校验所选地址是否可达
    if (val && paymentType === PaymentTypeEnum.Authority) {
      fetchExpressAbility(val, currentExpress)
        ?.then(({ hasAbility }) => {
          // 地址可揽收则计算运费
          if (hasAbility) {
            hasAbilityCb(val, currentExpress);
          }
        })
        .catch(() => {
          // 接口异常降级为可揽收
          hasAbilityCb(val, currentExpress);
        });
    }
  };

  const initAddressInfo = (expressId, paymentType, currentExpress = express) => {
    const storageData = getExpressWayBillStorage(
      currentElectronWayBillServiceKdtId,
      expressId,
      paymentType
    );

    // 有赞寄件看 fakeId
    const id = paymentType === PaymentTypeEnum.Authority ? storageData.fakeId : storageData.auditNo;

    // 如果有auditNo缓存并且地址返回了此地址，则直接调用change事件
    if (id && currentExpress.expressAddrList?.find((item) => item.auditNo === id)) {
      handleExpressAddressChange(id, currentExpress);
    } else {
      // 品牌变更后，如果没有默认地址，设置为空
      handleExpressAddressChange('');
    }
  };

  // 快递品牌变更
  const handleExpressBrandChange = (brandCode, currentExpress = express) => {
    const { expressId, paymentType } = currentExpress;
    if (!brandCode) {
      return;
    }
    onExpressValueChange({
      brandCode,
      eWaybillTemplateId: ''
    });
    if (expressId) {
      onGetNewWayBillAddress({
        expressId,
        paymentType,
        waybillVersion,
        brandCode
      }).then((addresses) => {
        initAddressInfo(expressId, paymentType, { ...currentExpress, expressAddrList: addresses });
      });

      fetchAndInitExpressTemplate({
        expressId,
        paymentType,
        brandCode
      });
    }
  };

  const fetchAndInitExpressBrand = (currentExpress) => {
    const { expressId, paymentType } = currentExpress;
    const storageData = getExpressWayBillStorage(
      currentElectronWayBillServiceKdtId,
      expressId,
      paymentType
    );
    getExpressBrands({
      expressId,
      onlyAvailable: 1
    }).then((expressBrands) => {
      setExpressBrands(expressBrands || []);
      // 如果有brandCode缓存并且返回了此数据，则直接调用change事件
      if (
        storageData.brandCode &&
        expressBrands.find((item) => item.brandCode === storageData.brandCode)
      ) {
        return handleExpressBrandChange(storageData.brandCode, currentExpress);
      }
      // 如果只有一个，直接选中
      if (expressBrands.length === 1) {
        return handleExpressBrandChange(expressBrands[0].brandCode, currentExpress);
      }
      // 否则，选择默认的顺丰速运
      const item = expressBrands.find((item) => item.isDefault);
      if (item) {
        return handleExpressBrandChange(item.brandCode, currentExpress);
      }
    });
  };

  const getSendType = (allType) => {
    let sendType;
    const wayBillTypeStorage = getWayBillTypeStorage(currentElectronWayBillServiceKdtId);
    if (allType.length === 1) {
      sendType = allType[0];
    } else if (allType.includes(wayBillTypeStorage)) {
      sendType = wayBillTypeStorage;
    } else if (selectedExpress.sendType) {
      sendType = selectedExpress.sendType;
    } else if (allType.includes(4) /* 默认的最后兜底是 快递员上门打印电子面单 */) {
      sendType = 4 /* 快递员上门打印面单 */;
    } else if (!sendType && allType.length > 0) {
      // 这个是最后的兜底，allType中4不是所有都有的，比如自结算的顺丰速运
      sendType = allType[0];
    }

    return sendType;
  };

  const handleValidAddress = (val, addresses) => {
    const currentAddress = addresses.find((res) => res.id === val);
    if (!currentAddress) return;

    const { cityName, provinceName } = currentAddress;
    const { expressId } = selectedExpress;
    const params = { sendCity: cityName, sendProvince: provinceName, expressId };

    queryDeliveryBizTypeByExpressAndAddress(params).then((res) => {
      if (res && Array.isArray(res)) {
        setSTAllType(res);

        const sendType = getSendType(res);
        onExpressValueChange({ sendType });
      }
    });
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleExpressIdChanged = (expressId, paymentType, defaultSelectedExpress = {}) => {
    if (systemCallExpressCompanies.length === 0 && !defaultSelectedExpress.expressId) return;
    const selectedExpress =
      find(systemCallExpressCompanies, { expressId, paymentType }) || defaultSelectedExpress;
    if (selectedExpress?.channelStatus === 2) return; // 渠道不可用

    setSelectedExpress(selectedExpress);
    setTemplates([]);
    setProductTypes([]);
    setAppendServiceList([]);
    setExpressBrands([]);
    setExpressFee(0);

    const allType = selectedExpress?.allType || [];

    const initParams = {
      sendType: getSendType(allType),
      expressId: selectedExpress.expressId,
      expressName: selectedExpress.expressName,
      pickTime,
      isPay: selectedExpress.isPay,
      auditNo: '', // 快递公司变更，auditNo重置
      eWaybillTemplateId: '', // 面单模板重置
      fakeId: '',
      logisticsServicesView: {}, // 增值服务重置
      productCode: '', // 产品类型重置
      brandCode: '', // 品牌
      expressFee: 0, // 快递预估价设置为0
      weight: selectedExpress.paymentType === PaymentTypeEnum.Authority ? 1000 : undefined, // 有赞寄件重量默认 1kg
      depositOffline: selectedExpress.depositOffline || true,
      latticePointDetailModel: selectedExpress.latticePointDetailModel || [],
      paymentType: selectedExpress.paymentType
    };
    onExpressValueChange(initParams);

    const currentExpress = { ...initParams };
    // 新电子面单顺丰不请求地址，因为还需要依赖品牌的选择
    if (expressId !== SF_EXPRESS_CODE) {
      onGetNewWayBillAddress({
        expressId: selectedExpress.expressId,
        paymentType: selectedExpress.paymentType,
        waybillVersion,
        brandCode: selectedExpress.brandCode
      }).then((addresses) => {
        currentExpress.expressAddrList = addresses;
        initAddressInfo(expressId, paymentType, currentExpress);
      });
    }

    // 顺丰需要选择品牌后请求面单模板
    if (expressId !== SF_EXPRESS_CODE) {
      // 面单模板
      fetchAndInitExpressTemplate(selectedExpress);
    } else {
      // 快递品牌
      fetchAndInitExpressBrand(currentExpress);
    }

    updateDeliveryExpress();
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        handleExpressIdChanged
      };
    },
    [handleExpressIdChanged]
  );

  const initPickTime = () => {
    const pickDays = [
      {
        value: 0,
        text: '今天'
      },
      {
        value: 1,
        text: '明天'
      },
      {
        value: 2,
        text: '后天'
      }
    ];
    const currentHour = new Date().getHours();
    const end = 17;
    let start = currentHour < 9 ? 9 : currentHour + 1;
    if (start >= end) {
      pickDays.shift();
      start = 9;
    }
    setPickDays(pickDays);
    setPickStart(start);
    setPickTime({
      day: pickDays[0].value,
      time: start === end ? 9 : start
    });
  };

  useEffect(() => {
    initPickTime();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (channelType === DeliveryChannelType.WxChannelShop) {
    return (
      <ExpressWxChannelShopEWaybill
        express={express}
        orderInfo={orderInfo}
        onSystemExpressCompanyChange={onSystemExpressCompanyChange}
        onExpressAddressChange={onExpressAddressChange}
        onSendTypeChange={onSendTypeChange}
        onPrinterChange={onPrinterChange}
        onChangeExpress={onChangeExpress}
      />
    );
  }

  // 在线下单，发货弹窗直接抛出的异常，
  if (
    electronicSheetExceptionDesc &&
    (isNewWayBill ? electronicSheetExceptionCode !== ELECTRON_WAYBILL_SERVICE_CLOSED : true)
  ) {
    const linkInfo = ELECTRON_ERROR_INFO[electronicSheetExceptionCode];
    return (
      <p className="orange pl100">
        {electronicSheetExceptionDesc}
        {linkInfo && <BlankLink href={linkInfo.link}>{linkInfo.text}</BlankLink>}
      </p>
    );
  }

  const disableWeightInput = !express.auditNo;

  const { configErrorDesc, configErrorCode, YZShoppingInfo } = express;
  const errorLinkInfo =
    isNewWayBill && configErrorCode === ELECTRON_WAYBILL_SERVICE_CLOSED
      ? null
      : ELECTRON_ERROR_INFO[configErrorCode];

  // eslint-disable-next-line no-param-reassign
  const isPlaceOrder = placeType === DeliveryPlaceType.Order;
  const rechargeUrl = isPlaceOrder
    ? `${BASE_URL}/shop/v2/trade/newsettlement#/rechargeMoney`
    : `${BASE_URL}/v4/assets/recharge`;
  const inputDetail = (
    <>
      {express.sendType > 0 && express.sendType !== ONLY_DZMD && (
        // 不是仅电子面单的都要显示
        <div className="express-weight">
          <div className="express-content-item">
            <div className="express-content-item__label">取件时间：</div>
            <div className="express-content-item__value">
              {/* 这块逻辑现在在前端： 顺丰的尽快上门取件，其他的都是一样的选取时间取件 */}
              {express.expressId === SF_EXPRESS_CODE ? (
                <TimeSelect value={express.startTime} onChange={handlePickUpTimeChange} />
              ) : (
                <Select value={0}>
                  <Option value={0}>尽快上门取件</Option>
                </Select>
              )}
            </div>
          </div>
          {express.expressId === JD_EXPRESS_CODE && (
            <>
              <div className="express-content-item">
                <div className="express-content-item__label">物品重量：</div>
                <div className="express-content-item__value weight-input">
                  <NumberInput
                    showStepper
                    onChange={handleExpressWeightChange}
                    value={express.weight}
                    max={30}
                    placeholder="最大30千克"
                    disabled={disableWeightInput}
                    min={1}
                    decimal={0}
                  />
                  <span className="after-input-text">kg</span>
                  {disableWeightInput && <span className="check-address">请先选择地址</span>}
                </div>
              </div>

              <div className="express-content-item">
                <div className="express-content-item__label">面单模板：</div>
                <div className="express-content-item__value weight-input">
                  <Select
                    data={express.eWaybillTemplateOptions}
                    optionValue="id"
                    optionText="name"
                    onChange={(e) => {
                      onChangeExpress({
                        eWaybillTemplateId: e.target.value
                      });
                    }}
                    autoWidth
                    placeholder="请选择"
                    value={express.eWaybillTemplateId}
                  />
                </div>
              </div>
            </>
          )}
          {express.postage > 0 && (
            <div className="postage-info grey">
              <div>
                预估运费：
                {convertFenToYen(express.postage)}元 (
                {isRetailSingleStore && isPlaceOrder
                  ? '预估费用仅供参考，快递公司称重后，有赞将按照实际计费重量扣费'
                  : '实际重量以快递公司称重为准，运费有赞提前预扣，多退少补'}
                )
              </div>
              {!express.isCashPledgeEnough && (
                <p>
                  账户余额不足，
                  <BlankLink href={rechargeUrl}>请先充值</BlankLink>
                </p>
              )}
            </div>
          )}
        </div>
      )}
      <div className="express-content-item">
        <div className="express-content-item__label">打印机：</div>
        <div className="express-content-item__value">
          {printerErrDesc ? (
            <p className="orange">{printerErrDesc}</p>
          ) : (
            <div>
              <Select
                data={printerList}
                optionValue="equipmentNumber"
                optionText="name"
                onChange={onPrinterChange}
                autoWidth
                placeholder="请选择打印机"
                value={express.printerDeviceNo}
              />
              <BlankLink href={`${BASE_URL}/setting/common/device#/`}>新增</BlankLink>
            </div>
          )}
        </div>
      </div>
    </>
  );

  const renderPickTime = () => {
    const { pickTime = {} } = express;
    const end = 17;
    const start = pickTime.day === 0 ? pickStart || 9 : 9;
    const pickTimes = new Array(end - start).fill(0).map((_, index) => ({
      value: start + index,
      text: `${prefixZero(start + index)}:00 - ${prefixZero(start + index + 1)}:00`
    }));
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          {!find(EXPRESS_SUPPORT_DETAIL_PICK_TIME, (id) => id === selectedExpress.expressId) ? (
            <FormSelectField
              key="pickTime"
              label="取件时间："
              name="pickTime"
              value={0}
              data={[
                {
                  value: 0,
                  text: '尽快上门取件'
                }
              ]}
            />
          ) : (
            <div className="pick-time-field">
              <FormSelectField
                className="inline"
                label="取件时间："
                data={pickDays}
                name="pickDay"
                value={pickTime.day}
                onChange={(val) =>
                  onExpressValueChange({
                    pickTime: {
                      ...express.pickTime,
                      day: val
                    }
                  })
                }
                validations={{ required: true }}
                validationErrors={{ required: '请选择取件时间' }}
              />
              <Select
                value={pickTime.time}
                data={pickTimes}
                onChange={(e) =>
                  onExpressValueChange({
                    pickTime: {
                      ...express.pickTime,
                      time: +e.target.value
                    }
                  })
                }
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  // 面单模板
  const renderPrintTemplate = () => {
    const { eWaybillTemplateId } = express;

    const templatesView = templates.map((item) => {
      return {
        ...item,
        label: item.templateName
      };
    });
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <FormSelectField
            className="inline"
            autoWidth
            width="200px"
            name="eWaybillTemplateId"
            label="面单模板："
            value={eWaybillTemplateId}
            data={templatesView}
            required={express.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value}
            optionText="label"
            optionValue="templateUrl"
            validations={{ required: true }}
            validationErrors={{ required: '请选择面单模板' }}
            onChange={(val) => {
              onExpressValueChange({ eWaybillTemplateId: val });
            }}
          />
        </div>
      </div>
    );
  };

  const getAddressManagePageText = (len, paymentType) => {
    if (len === 0) {
      return '去开通';
    }
    if (paymentType === PaymentTypeEnum.Oneself) {
      return '管理发货地址';
    }
    return '新增发货地址';
  };

  const getAddressManagePageUrl = (autoOpen) => {
    const { expressId, paymentType } = selectedExpress;
    if (!isNewWayBill) {
      return `${BASE_URL}/v4/ump/logisticsService/serviceManage?code=${expressId}`;
    }
    const rootUrl =
      // eslint-disable-next-line no-nested-ternary
      paymentType === PaymentTypeEnum.Oneself ? ELECTRON_WAY_BILL_URL : SHOP_ADDRESS_CREATE_URL;
    if (autoOpen) {
      return `${rootUrl}?code=${expressId}&paymentType=${paymentType}&scene=OPEN_SERVICE`;
    }
    return rootUrl;
  };

  /** 是否是当前店铺发货,排除总部帮仓库等发货的情况 */
  const isCurrentKdtDelivery = currentElectronWayBillServiceKdtId === window._global.kdtId;
  let selectedAddressDisabled = express.expressAddrList?.length === 0;
  if (isNewWayBill) {
    selectedAddressDisabled =
      selectedAddressDisabled ||
      (YZShoppingInfo?.waitJoin && selectedExpress?.recommend && !express.agreeProtocol);
  }

  let abilityErrorMsg = '';
  const currentAddress = express.expressAddrList.find(
    (res) => res.auditNo === (express.auditNo || express.fakeId)
  );
  const hasAbility = currentAddress?.hasAbility;
  if (!hasAbility) {
    abilityErrorMsg = currentAddress?.reason || '';
  }

  const handleNewBillWayWeightChange = (value) => {
    const weight = +value * 1000;
    const isPay = selectedExpress.expressId && selectedExpress.isPay;
    onExpressValueChange({ weight });
    if (isPay) {
      // 申通快递灰度中，更新快递列表
      updateDeliveryExpress?.();
      if (timer.current) {
        clearTimeout(timer.current);
      }
      timer.current = window.setTimeout(() => {
        fetchExpressFee(weight, express);
      }, 500);
    }
  };

  // 快递品牌
  const renderExpressBrand = () => {
    const { brandCode } = express;
    return (
      <div className="express-content-item">
        <div className="express-content-item__label">快递品牌：</div>
        <RadioGroup
          value={brandCode}
          onChange={(e) => handleExpressBrandChange(e.target.value, express)}
        >
          {expressBrands.map((item) => {
            return (
              <Radio key={item.brandCode} value={item.brandCode}>
                {item.brandName}
              </Radio>
            );
          })}
        </RadioGroup>
      </div>
    );
  };

  // 新电子面单发货类型
  const renderExpressWayBillTypeSelection = () => {
    if (!selectedExpress.allType) {
      return null;
    }
    return (
      <div className="express-content-item">
        <div className="express-content-item__label">发货类型：</div>
        <RadioGroup
          value={express.sendType}
          onChange={(e) => onExpressValueChange({ sendType: +e.target.value })}
        >
          {selectedExpress.allType.map((type) => {
            const key = EXPRESS_WAY_BILL_TYPES_MAP[type];
            return (
              <>
                <Radio
                  key={type}
                  value={EXPRESS_WAY_BILL_TYPES[key].value}
                  style={{ verticalAlign: 'top' }}
                >
                  {EXPRESS_WAY_BILL_TYPES[key].text}
                  {type === 4 && <div className="text-gray">单日发货5单以上建议自己打印面单</div>}
                </Radio>
              </>
            );
          })}
        </RadioGroup>
      </div>
    );
  };

  // 新电子面单-打印机
  const renderPrinterSelection = () => {
    const noPrinter = printerList.length === 0;
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <FormSelectField
              className="inline"
              autoWidth
              width="200px"
              name="printer"
              label="打印机："
              data={printerList}
              value={express.printerDeviceNo}
              required={express.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value}
              disabled={noPrinter}
              optionText="name"
              optionValue="id"
              onChange={(val) => onExpressValueChange({ printerDeviceNo: val })}
              validations={{ required: true }}
              validationErrors={{ required: '请选择一台打印机' }}
            />
            <span>
              <a style={{ cursor: 'pointer' }} onClick={() => fetchPrinterList(true)}>
                刷新
              </a>
            </span>
          </div>
          {noPrinter && (
            <div className="express-way-bill-cainiao-printer-tips">
              未识别到可选择的打印机，请先<strong>打开云打印组件；</strong>如果未安装组件，请先
              <a
                // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
                href="https://page.cainiao.com/waybill/cloud_printing/home.html?spm=a262a.ap-detail.0.0.5aec3651pQobvi"
                target="_blank"
                rel="noreferrer"
              >
                下载安装
              </a>
              。
              <a
                href={setUrlDomain('/displaylist/detail_4_4-2-85545', 'help')}
                target="_blank"
                rel="noreferrer"
              >
                查看打印组件使用教程
              </a>
            </div>
          )}
        </div>
      </div>
    );
  };

  // 新电子面单-产品类型
  const renderProductType = () => {
    const { productCode } = express;
    // 非自结算顺丰不展示
    if (
      !(
        selectedExpress.expressId === SF_EXPRESS_CODE &&
        selectedExpress.paymentType === PaymentTypeEnum.Oneself
      )
    ) {
      return null;
    }
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <FormSelectField
            className="inline"
            autoWidth
            width="200px"
            name="productCode"
            label="产品类型："
            data={productTypes}
            value={productCode}
            optionText="label"
            optionValue="value"
            resetOption
            resetText="重置"
            onChange={(val) => onExpressValueChange({ productCode: val || '' })}
          />
        </div>
      </div>
    );
  };

  // 新电子面单-增值服务
  const renderAppendService = () => {
    // 官方寄件不展示 || 没有可选增值服务不展示
    if (appendServiceList.length === 0 || express.paymentType === PaymentTypeEnum.Authority) {
      return null;
    }
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <AppendServiceSelection
            value={express.logisticsServicesView}
            dataSource={appendServiceList}
            onChange={(val) => {
              onExpressValueChange({
                logisticsServicesView: val
              });
            }}
          />
        </div>
      </div>
    );
  };

  // 新电子面单-包裹重量
  const renderPackageWeight = () => {
    return (
      <div className="express-content-item">
        <div className="express-content-item__value">
          <FormNumberInputField
            onChange={handleNewBillWayWeightChange}
            value={1}
            min={1}
            max={30}
            decimal={1}
            showStepper
            name="weight"
            label="包裹重量："
            width="160px"
            required
            validations={{
              required: true
            }}
            validationErrors={{
              required: '请填写物品重量'
            }}
            className="delivery-content__express-weight-form"
            addonAfter="kg"
          />
        </div>
      </div>
    );
  };

  const renderPredicatePrice = () => {
    const isPay = selectedExpress.expressId && selectedExpress.isPay;
    const { overdueFee, overdueLimitFee } = express.YZShoppingInfo || {};

    if (!isPay) {
      return null;
    }

    const fee = expressFee ? (expressFee / 100).toFixed(2) : 0;

    const getFeeDesc = isEditExpress
      ? '此为单个包裹预估费用，快递公司称重后，有赞将按照实际计费重量扣费'
      : '预估费用仅供参考，快递公司称重后，有赞将按照实际计费重量扣费';

    return (
      <div className="express-way-bill-fee-line">
        <span className="express-way-bill-fee-line_label">预估费用：</span>
        <span className="express-way-bill-fee-line_content">
          <span className="express-way-bill-fee-line_content-price">{fee} 元</span>（{getFeeDesc}）
        </span>
        {subsidyFee ? (
          <div className="express-way-bill-fee-line_subsidy">运费补贴{subsidyFee / 100}元</div>
        ) : null}
        {overdueFee >= overdueLimitFee && (
          <div className="express-way-bill-fee-line_balance-warn">
            有赞寄件待结算费用：{formatMoney(overdueFee)}元，请尽快<a href={RECHARGE_URL}>充值</a>
          </div>
        )}
      </div>
    );
  };

  let showRest = selectedExpress.expressId > 0 && !!(express.auditNo || express.fakeId);
  const { isPay } = selectedExpress;
  if (isPay) {
    // 有赞寄件，未开通且未勾选开通的不展示更多表单项
    if (express.YZShoppingInfo?.waitJoin && !express.agreeProtocol) {
      showRest = false;
    }
  }

  return (
    <div>
      <div className="express-content-item">
        <div className="express-content-item__label">
          {isNewWayBill ? '快递公司：' : '物流公司：'}
        </div>
        <div className="express-content-item__value">
          <div className="express-content-item__express-company-container">
            {systemCallExpressCompanies.map((item) => {
              let originPrice = item.logisticsRecommendInfo?.originPrice;
              let realPrice = item.logisticsRecommendInfo?.estimateAmount;
              // 新电子面单-有赞寄件，展示根据地址和重量新算的运费
              if (isNewWayBill && item.paymentType === PaymentTypeEnum.Authority) {
                const currentExpress = YZExpressFeeList.find(
                  (res) => res.expressId === +item.expressId
                );
                if (currentExpress) {
                  originPrice = formatMoney(currentExpress.originPrice);
                  realPrice = formatMoney(currentExpress.realPrice);
                }
              }
              return (
                <div
                  key={`${item.expressId}_${item.paymentType}`}
                  className={`express-content-item__express-company-item ${
                    item.expressId === express.expressId &&
                    (isNewWayBill ? item.paymentType === express.paymentType : true)
                      ? 'express-content-item__express-company-item-checked'
                      : ''
                  }`}
                  onClick={() => {
                    if (!isNewWayBill) {
                      onSystemExpressCompanyChange({ target: { value: +item.expressId } });
                    } else {
                      onExpressValueChange({
                        expressId: item.expressId,
                        paymentType: item.paymentType
                      });
                      handleExpressIdChanged(+item.expressId, item.paymentType);
                    }
                  }}
                >
                  {isNewWayBill ? (
                    <>
                      <div className="title">
                        {item.expressName}{' '}
                        {item.recommend && <span className="recommend">有赞寄件</span>}
                      </div>
                      {realPrice && (
                        <div className="tips">
                          <span>预估：</span>
                          <span className="text-price">
                            <span>¥</span>
                            <span className="text-price-int">{priceSplitFormat(realPrice)[0]}</span>
                            <span className="text-price-float">
                              .{priceSplitFormat(realPrice)[1]}
                            </span>
                          </span>
                          {originPrice && Number(realPrice) < Number(originPrice) && (
                            <span className="text-origin-price">
                              ¥{priceSplitFormat(originPrice).join('.')}
                            </span>
                          )}
                        </div>
                      )}
                    </>
                  ) : (
                    <>
                      <div className="title">
                        {item.expressName}{' '}
                        {item.recommend && <span className="recommend">官方推荐</span>}
                      </div>
                      {Object.keys(item.logisticsRecommendInfo || {}).length > 0 && (
                        <div className="tips">{`最低${item.logisticsRecommendInfo.discount}折，每单最高省${item.logisticsRecommendInfo.discountAmount}元`}</div>
                      )}
                    </>
                  )}
                </div>
              );
            })}
          </div>
          {isNewWayBill && isCurrentKdtDelivery && (
            <div className="express-content-item__express-company-action">
              <a href="/v4/trade/electron-way-bill#/manage?scene=OPEN_SERVICE" target="_blank">
                添加服务商
              </a>
              <a
                onClick={() => {
                  // 服务商刷新，先清空服务商信息，刷新服务商数据后，根据目前选中的默认服务商重新执行选择逻辑
                  onExpressValueChange(
                    {
                      ...defaultExpressData.express,
                      pickTime,
                      YZShoppingInfo
                    },
                    true
                  );

                  setSelectedExpress([]);

                  fetchElectronCompanies(isNewWayBill, currentElectronWayBillServiceKdtId).then(
                    () => {
                      const defaultExpressKey =
                        window.localStorage.getItem(
                          `${EXPRESS_LOCAL_STORAGE_KEY}_${currentElectronWayBillServiceKdtId}`
                        ) || '';

                      if (defaultExpressKey) {
                        const [defaultExpressId, defaultPaymentType] = defaultExpressKey.split('_');
                        if (defaultExpressId) {
                          handleExpressIdChanged(+defaultExpressId, +defaultPaymentType);
                        }
                      }
                    }
                  );
                }}
              >
                刷新
              </a>
            </div>
          )}
          {YZShoppingInfo?.waitJoin &&
            selectedExpress.recommend &&
            (isCurrentKdtDelivery ? (
              <>
                <Checkbox
                  className="express-content-item__express-company-protocol"
                  checked={express.agreeProtocol}
                  onChange={(e) => {
                    onExpressValueChange({ agreeProtocol: e.target.checked });
                  }}
                >
                  <div>
                    开通有赞寄件{' '}
                    <span style={{ color: '#ED6A18' }}>
                      无需预充值，单单享运费补贴！
                      <a
                        href={setUrlDomain(
                          '/v4/assets/shipping?source=order-delivery-dialog',
                          'store'
                        )}
                        target="_blank"
                        rel="noreferrer"
                      >
                        查看详情
                      </a>
                    </span>
                  </div>
                  <div style={{ color: '#999' }}>
                    勾选即表示已阅读并同意
                    <a
                      href={setUrlDomain(
                        '/intro/rule/detail?alias=js9mzaf9&pageType=agreements',
                        'youzan'
                      )}
                      target="_blank"
                      rel="noreferrer"
                    >
                      《有赞寄件服务协议》
                    </a>
                    ，同意开通有赞寄件统一接入服务商，预计费用详见
                    <a href={selectedExpress.freightStandardUrl} target="_blank" rel="noreferrer">
                      运费说明
                    </a>
                  </div>
                </Checkbox>
                {!express.agreeProtocol && (
                  <p style={{ color: '#D42F15', fontSize: '12px' }}>请勾选“开通有赞寄件”</p>
                )}
              </>
            ) : (
              !!currentElectronWayBillServiceKdtName && (
                <div
                  className="express-content-item__express-company-protocol"
                  style={{ color: '#ED6A18' }}
                >
                  {`店铺：${currentElectronWayBillServiceKdtName}未开通有赞寄件服务，请先开通`}
                </div>
              )
            ))}
        </div>
      </div>

      {isNewWayBill &&
        selectedExpress.expressId === SF_EXPRESS_CODE &&
        selectedExpress.paymentType === PaymentTypeEnum.Oneself &&
        expressBrands.length > 0 &&
        renderExpressBrand()}
      {isNumber(express.expressId) && (
        <>
          <div className="express-content-item">
            <div className="express-content-item__label">发货地址：</div>
            <div className="express-content-item__value">
              <Select
                data={express.expressAddrList}
                optionValue="auditNo"
                optionText="displayAddress"
                className={cx('express-address', {
                  'error-field': abilityErrorMsg
                })}
                autoWidth
                placeholder="请选择发货地址"
                onChange={(e) => {
                  if (isNewWayBill) {
                    updateDeliveryExpress();
                    handleExpressAddressChange(e.target.value);
                    if (selectedExpress?.expressId === ST_EXPRESS_CODE) {
                      handleValidAddress(e.target.value, express.expressAddrList);
                    }
                  } else {
                    onExpressAddressChange(e);
                  }
                }}
                value={express.auditNo || express.fakeId}
                disabled={selectedAddressDisabled}
              />
              {(!isNewWayBill || isCurrentKdtDelivery) && (
                <>
                  <BlankLink href={getAddressManagePageUrl(express.expressAddrList?.length || 0)}>
                    {getAddressManagePageText(
                      express.expressAddrList?.length || 0,
                      express.paymentType
                    )}
                  </BlankLink>
                  <BlankLink
                    onClick={() => {
                      if (!isNewWayBill) {
                        onSystemExpressCompanyChange({ target: { value: express.expressId } });
                      } else {
                        onGetNewWayBillAddress({
                          expressId: express.expressId,
                          paymentType: express.paymentType,
                          waybillVersion,
                          brandCode: express.paymentType
                        });
                      }
                    }}
                    style={{ marginLeft: '10px' }}
                  >
                    刷新
                  </BlankLink>
                </>
              )}

              {abilityErrorMsg && <p className="error-info">{abilityErrorMsg}</p>}
              {/**
               * 1、新的发货弹窗；
               * 2、分店开了有赞寄件；
               * 3、总店帮助分店发货；
               * 4、分店发货地址为空；
               * */}
              {isNewWayBill &&
                !(YZShoppingInfo?.waitJoin && selectedExpress.recommend) &&
                !isCurrentKdtDelivery &&
                express.expressAddrList?.length === 0 && (
                  <div style={{ color: '#ED6A18', marginTop: 10 }}>
                    履约店铺未设置发货地址，请通知履约店铺在地址库中添加
                  </div>
                )}
            </div>
          </div>

          {!isNewWayBill && express.sendTypes.length > 0 && (
            <div className="express-content-item">
              <div className="express-content-item__label">发货类型：</div>
              <div className="express-content-item__value">
                <RadioGroup value={express.sendType} onChange={onSendTypeChange}>
                  {express.sendTypes.map(({ code, deliveryType }) => (
                    <Radio value={code} key={code}>
                      {deliveryType}
                    </Radio>
                  ))}
                </RadioGroup>
              </div>
            </div>
          )}

          {isNewWayBill && showRest && renderExpressWayBillTypeSelection()}
          {
            // 这里的是配置异常
            errorLinkInfo && (
              <div className="postage-info grey">
                {configErrorDesc}，
                <BlankLink href={errorLinkInfo.link}>{errorLinkInfo.text}</BlankLink>
              </div>
            )
          }
          {!isNewWayBill && !get(errorLinkInfo, 'intercept') && inputDetail}

          {showRest && isNewWayBill && (
            <>
              {((express.sendType === 2 && express.paymentType !== PaymentTypeEnum.Authority) ||
                (express.paymentType === PaymentTypeEnum.Authority &&
                  selectedExpress.expressId === SF_EXPRESS_CODE)) &&
                renderPickTime()}
              {isPay && renderPackageWeight()}
              {express.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value &&
                renderPrintTemplate()}
              {renderProductType()}
              {renderAppendService()}
              {express.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value &&
                renderPrinterSelection()}
              {isPay && renderPredicatePrice()}
            </>
          )}
        </>
      )}
    </div>
  );
});

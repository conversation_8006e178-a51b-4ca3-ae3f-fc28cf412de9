import * as React from 'react';
import { Alert, Button, Dialog, Grid, IGridColumn, Icon } from 'zent';

const { openDialog, closeDialog } = Dialog;

export type IGridItem = {
  conditionDesc: string;
  unPassDesc?: string;
  link?: string;
  pass: boolean;
};
interface Props {
  data: IGridItem[];
  onClose: () => void;
}

class Require extends React.Component<Props> {
  public handleClose = () => {
    this.props.onClose();
  };

  public getColumns(): IGridColumn[] {
    return [
      {
        title: '开通条件',
        name: 'conditionDesc',
        width: '50%'
      },
      {
        title: '是否达成',
        name: 'pass',
        bodyRender: (data: IGridItem) => {
          if (data.pass) {
            return <Icon type="check-circle" className="sucess-icon" />;
          }
          return <Icon type="close-circle" className="fail-icon" />;
        }
      },
      {
        title: '任务入口',
        bodyRender: (data: IGridItem) => {
          if (data.pass) {
            return '--';
          }
          if (data.link) {
            return (
              <a href={data.link} target="_blank" rel="noopener noreferrer">
                {data.unPassDesc}
              </a>
            );
          }
          return data.unPassDesc;
        }
      }
    ];
  }

  public render() {
    const { data } = this.props;
    return (
      <div className="apply-requirement-content">
        <Alert type="warning">店铺暂不可开通有赞寄件，请检查是否满足以下开通条件。</Alert>
        <Grid
          className="require-table"
          rowKey="conditionNo"
          columns={this.getColumns()}
          datasets={data}
        />
        <div className="apply-progress-footer">
          <Button type="primary" onClick={this.handleClose}>
            我知道了
          </Button>
        </div>
      </div>
    );
  }
}

const openRequire = (list: IGridItem[] = []) => {
  const id = 'require';
  openDialog({
    dialogId: id,
    maskClosable: false,
    className: 'od-apply-requirement',
    children: <Require onClose={() => closeDialog(id, { triggerOnClose: true })} data={list} />,
    title: '开通有赞寄件'
  });
};

export default openRequire;

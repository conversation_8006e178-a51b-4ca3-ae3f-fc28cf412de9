import React from 'react';
// import { Select } from '@youzan/retail-components';
import { Select } from '@youzan/biz-select-center';
import {
  addDays,
  format,
  getHours,
  isAfter,
  isToday,
  isTomorrow,
  setHours,
  subDays
} from 'date-fns';
import { find, get } from 'lodash';

const TODOY_START_TIME = new Date().setHours(0, 0, 0, 0);
const HOUR_MS = 3600000;

const TODAY_VALUE = 0;
const TOMORROW_VALUE = 1;
const AFTER_TOMORROW_VALUE = 2;

const DAY_CHECK_MAP = {
  [TODAY_VALUE]: isToday,
  [TOMORROW_VALUE]: isTomorrow,
  [AFTER_TOMORROW_VALUE]: (date) => isTomorrow(subDays(date, 1))
};

const baseDateSelect = [
  { value: TODAY_VALUE, text: '今天' },
  { value: TOMORROW_VALUE, text: '明天' },
  { value: AFTER_TOMORROW_VALUE, text: '后天' }
];

const baseTimeSelect = [
  {
    value: 9,
    text: '09:00~10:00',
    disabled: false
  },
  {
    value: 10,
    text: '10:00~11:00',
    disabled: false
  },
  {
    value: 11,
    text: '11:00~12:00',
    disabled: false
  },
  {
    value: 12,
    text: '12:00~13:00',
    disabled: false
  },
  {
    value: 13,
    text: '13:00~14:00',
    disabled: false
  },
  {
    value: 14,
    text: '14:00~15:00',
    disabled: false
  },
  {
    value: 15,
    text: '15:00~16:00',
    disabled: false
  },
  {
    value: 16,
    text: '16:00~17:00',
    disabled: false
  }
];

class TimeSelect extends React.Component {
  state = {
    date: Object.keys(DAY_CHECK_MAP).find((key) => DAY_CHECK_MAP[key](this.props.value)) || '',
    timeRange: get(
      baseTimeSelect.find(({ value: hour }) => getHours(this.props.value) === hour),
      'value',
      ''
    )
  };

  componentDidMount() {
    this.initData();
  }

  initData = () => {
    const initDataSelect = find(this.dateSelectData, ({ disabled }) => !disabled) || {};
    this.handleDateChange(initDataSelect.value);
  };

  get dateSelectData() {
    const deadline = TODOY_START_TIME + 16 * HOUR_MS;
    const isTodayDisable = isAfter(Date.now(), deadline);
    baseDateSelect[0].disabled = isTodayDisable;
    return baseDateSelect;
  }

  get timeSelectData() {
    if (this.state.date === TODAY_VALUE) {
      return baseTimeSelect.map(({ value, text }) => ({
        value,
        text,
        disabled: getHours(Date.now()) >= value
      }));
    }
    return baseTimeSelect;
  }

  propsChange = () => {
    const { date, timeRange } = this.state;

    const formatValue = format(setHours(addDays(TODOY_START_TIME, date), timeRange), 'x');
    this.props.onChange(formatValue);
  };

  handleDateChange = (val) => {
    let timeRange = baseTimeSelect[0].value;
    if (val === TODAY_VALUE) {
      timeRange = get(
        baseTimeSelect.find(({ value }) => getHours(Date.now()) < value),
        'value'
      );
    }

    this.setState(
      {
        timeRange,
        date: val
      },
      this.propsChange
    );
  };

  handleTimeChange = (value) => {
    this.setState({ timeRange: value }, this.propsChange);
  };

  render() {
    const { timeRange, date } = this.state;

    return (
      <div className="od-time-select">
        <Select
          width={75}
          className="date"
          value={date}
          onChange={this.handleDateChange}
          data={this.dateSelectData}
        />
        <Select value={timeRange} onChange={this.handleTimeChange} data={this.timeSelectData} />
      </div>
    );
  }
}

export default TimeSelect;

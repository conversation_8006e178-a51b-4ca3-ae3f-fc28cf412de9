import React from 'react';
import { Checkbox, ICheckboxEvent } from 'zent';
import { PopInfo } from '@youzan/retail-components';

export function DeliveryService({
  isDirectDelivery,
  onChange
}: {
  isDirectDelivery: boolean;
  onChange: (value: boolean) => void;
}) {
  function handleChange(e: ICheckboxEvent<boolean>) {
    onChange(e?.target?.checked || false);
  }
  return (
    <div className="input-item delivery-service">
      <div className="delivery-extra-content__label">配送服务：</div>
      <div className="delivery-extra-content__value">
        <Checkbox checked={isDirectDelivery} onChange={handleChange} />
        <span>专人直送</span>
        <PopInfo
          className="delivery-extra-content__value__pop-info"
          popContent="骑手同一时间只送一单，配送费高于普通配送。"
        />
      </div>
    </div>
  );
}

import { BlankLink } from '@youzan/react-components';
import React from 'react';
import { Alert } from 'zent';
import { track } from '@youzan/retail-utils';

const FulfillExpressComponentName = 'fulfill_express';

export const DeliveryAlert = ({ localBenefitSubsidyDesc, handleGoToUse }) => {
  if (!localBenefitSubsidyDesc) {
    return null;
  }

  React.useEffect(() => {
    track({
      et: 'view', // 事件类型
      ei: 'delivery_alert_view', // 事件标识
      en: '发货弹窗优惠礼包横幅曝光', // 事件名称
      params: {
        component: FulfillExpressComponentName
      },
    });
  }, []); // 空数组作为第二个参数，确保只在组件挂载时运行

  return (
    <Alert style={{ marginTop: 12 }} >
      {localBenefitSubsidyDesc}
      <BlankLink
        style={{ float: 'right' }}
        onClick={() => {
          track({
            et: 'click', // 事件类型
            ei: 'click_delivery_alert_use', // 事件标识
            en: '发货弹窗优惠礼包点击“去使用”', // 事件名称
            params: {
              component: FulfillExpressComponentName
            },
          });
          handleGoToUse();
        }}
      >
        去使用
      </BlankLink>
    </Alert>
  );
};


/**
 * 增值服务选择
 * dataSource为当前快递服务商支持的所有增值服务
 * 增值服务有些属于必选，则根据isRequire字段需要默认选中为true的选项，并且想要取消则报错
 * 选择的所有增值服务，根据needAttribute字段判断，是否有属性需要填写，如果有则展示所有属性字段
 * 所有表单项通过logisticsServicesView字段维护，格式：{"serviceCode":{"attributeCode":value}}，props.value引用此数据
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Form } from '@zent/compat';
import cloneDeep from 'lodash/cloneDeep';
import { Notify } from 'zent';
import { IAppendServiceAttributeVo, IAppendServiceVo } from './types';

const { FormSelectField, FormNumberInputField, FormInputField, FormDatePickerField } = Form;

const getValidationProps = ({ required, maxLength, minLength }) => {
  const options = {
    validations: {},
    validationErrors: {}
  } as any;
  if (required) {
    options.validations.required = true;
    options.validationErrors.required = '必填';
  }
  if (maxLength) {
    options.validations.maxLength = maxLength;
    options.validationErrors.maxLength = '超过最大长度';
  }
  if (minLength) {
    options.validations.maxLength = maxLength;
    options.validationErrors.maxLength = `长度不能小于${minLength}`;
  }
  return options;
};

interface IAttributeProps {
  value: any; // 属性值
  onChange: (attributeCode: string, value: any) => void;
  config: IAppendServiceAttributeVo; // 属性的配置
}
// 属性组件
const AttributeField = (props: IAttributeProps) => {
  const { value, config, onChange } = props;
  const { attributeCode, attributeName, attributeType, typeDescModel: fieldConfig } = config;

  const handleChange = (e) => {
    let value = e?.target ? e.target.value : e;
    // 根据attributeType的类型，number的话格式化一下
    if (value && attributeType === 'number') {
      value = +value;
    }
    onChange(attributeCode, value);
  };

  if (fieldConfig.type === 'string') {
    return (
      <FormInputField
        name={attributeCode}
        label={`${attributeName}：`}
        width={200}
        value={value}
        required={fieldConfig.required}
        onChange={handleChange}
        {...getValidationProps(fieldConfig)}
      />
    );
  }
  if (fieldConfig.type === 'number') {
    const { max = Infinity, min = -Infinity } = fieldConfig;
    return (
      <FormNumberInputField
        name={attributeCode}
        label={`${attributeName}：`}
        width={200}
        value={value}
        required={fieldConfig.required}
        onChange={(val) => {
          handleChange(val === '' ? val : +val);
        }}
        max={max}
        min={min}
        {...getValidationProps(fieldConfig)}
      />
    );
  }
  if (fieldConfig.type === 'enum') {
    const { desc } = fieldConfig;
    const options = Object.keys(desc).map((key) => {
      return {
        value: key,
        label: desc[key]
      };
    });
    return (
      <FormSelectField
        name={attributeCode}
        label={`${attributeName}：`}
        width={200}
        value={value}
        required={fieldConfig.required}
        optionText="label"
        onChange={handleChange}
        data={options}
        {...getValidationProps(fieldConfig)}
      />
    );
  }
  if (fieldConfig.type === 'date') {
    return (
      <FormDatePickerField
        name={attributeCode}
        label={`${attributeName}：`}
        width={200}
        value={value}
        required={fieldConfig.required}
        dateFormat="YYYY-MM-DD"
        onChange={handleChange}
      />
    );
  }
  return null;
};

/**
 * 格式化数据 - 增值服务变更时触发
 * @param value 当前的value值
 * @param serviceCodeList 当前选中的服务编码列表
 */
const formatValue = (value, serviceCodeList) => {
  return serviceCodeList.reduce((result, code) => {
    return {
      ...result,
      [code]: value[code] || {}
    };
  }, {});
};

interface IProps {
  dataSource: IAppendServiceVo[];
  value: Record<string, any>; // logisticsServicesView结构
  onChange: (value: any) => void; // 增值服务变更时触发(); // value变更
}
const AppendServiceSelection = (props: IProps) => {
  const { dataSource, value, onChange } = props;

  const [selectServiceCode, setSelectServiceCode] = useState<string[]>([]);

  // 增值服务默认值设置
  useEffect(() => {
    const defaultServiceCode = dataSource
      .filter((item) => item.isRequire)
      .map((item) => item.serviceCode);
    onChange(formatValue({}, defaultServiceCode));
    setSelectServiceCode(defaultServiceCode);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSource]);

  const viewData = useMemo(() => {
    return dataSource.map((vo) => ({ ...vo, closable: !vo.isRequire }));
  }, [dataSource]);

  // 增值服务选项改变
  const handleChange = (code) => {
    const nextSelectServiceCode = [...selectServiceCode, code];
    setSelectServiceCode(nextSelectServiceCode);
    onChange(formatValue(value, nextSelectServiceCode));
  };

  // 属性值变更
  const handleAttributeChange = (serviceCode, attributeCode, val) => {
    const nextValue = cloneDeep(value);
    nextValue[serviceCode][attributeCode] = val;
    onChange(nextValue);
  };

  // 删除选项
  const handleDelete = (item) => {
    const nextSelectServiceCode = selectServiceCode.filter((code) => code !== item.serviceCode);
    setSelectServiceCode(nextSelectServiceCode);
    if (item.isRequire) {
      Notify.error('该快递公司必选该服务，不可取消');
      // 为什么要延迟复制第二遍？因为delete回调无法控制不可删除，因此需要在延迟后重新恢复原来的值
      // TODO: zent应该要支持选项不可删除，支持后需要改掉这里
      Promise.resolve().then(() => {
        setSelectServiceCode(selectServiceCode);
      });
    } else {
      onChange(formatValue(value, nextSelectServiceCode));
    }
  };

  return (
    <div className="express-content-item">
      <div className="express-content-item__value">
        <FormSelectField
          className="inline"
          autoWidth
          width="340px"
          name="serviceCode"
          label="增值服务："
          data={viewData}
          optionText="serviceName"
          optionValue="serviceCode"
          value={selectServiceCode}
          onChange={handleChange}
          onDelete={handleDelete}
          tags
        />
        {Object.keys(value || {}).map((code) => {
          const item = dataSource.find((vo) => vo.serviceCode === code);
          const serviceConfig = value[code] || {};
          if (!item?.needAttribute) {
            return null;
          }
          return (item?.valueAddedAttributeModels || []).map((attributeConfig) => {
            const attributeValue = serviceConfig[attributeConfig.attributeCode];
            return (
              <div className="attribute-field">
                <AttributeField
                  key={code + attributeConfig.attributeCode}
                  value={attributeValue}
                  config={attributeConfig}
                  onChange={handleAttributeChange.bind(null, code)}
                />
              </div>
            );
          });
        })}
      </div>
    </div>
  );
};

export default AppendServiceSelection;

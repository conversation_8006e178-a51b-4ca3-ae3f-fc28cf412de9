import { ExpressType } from '@youzan/order-domain-definitions';
import React from 'react';
import { UserPrivacyViewButton, useUserPrivacyData } from '../../user-privacy';

const { SelfFetch } = ExpressType;

const getDisplayInfo = (
  { distType, distTypeDesc = '' },
  { consigneeName = '', consigneeTelphone = '', selfFetchTime = '', consigneeAddress = '' }
) => {
  const displayInfo = {};
  const isSelflfetch = distType === SelfFetch;

  displayInfo.infos = isSelflfetch
    ? [
        {
          title: '自提人：',
          value: `${consigneeName} ${consigneeTelphone}`,
          showUserPrivacyViewBtn: true
        },
        { title: '自提时间：', value: `${selfFetchTime}` },
        { title: '自提地址：', value: `${consigneeAddress}` }
      ]
    : [
        { title: '配送方式：', value: `${distTypeDesc}` },
        {
          title: '收货人：',
          value: `${consigneeName} ${consigneeTelphone}`,
          showUserPrivacyViewBtn: true
        },
        { title: '收货地址：', value: `${consigneeAddress}` }
      ];

  return displayInfo;
};

const ExpressInfo = ({ data = {} }) => {
  const { distInfo = {}, encryptStr } = data;

  const { data: consigneeInfo, decrypt } = useUserPrivacyData({
    encryptKey: encryptStr,
    data: data.consigneeInfo,
    mapDecryptedData: (data) => data.consigneeInfo
  });

  const { infos } = getDisplayInfo(distInfo, consigneeInfo);

  return (
    <>
      {infos.map((item) => (
        <div className="express-content-item" key={item.title}>
          <div className="express-content-item__label">{item.title}</div>
          <div className="express-content-item__value">
            {item.value}
            {item.showUserPrivacyViewBtn && (
              <UserPrivacyViewButton className="od-privacy-button-gap" onDecrypt={decrypt} />
            )}
          </div>
        </div>
      ))}
    </>
  );
};

export default ExpressInfo;

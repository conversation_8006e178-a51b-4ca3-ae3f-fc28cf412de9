.notice-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: rgba(255, 239, 230, 1);
  border: 1px solid rgba(255, 239, 230, 1);
  border-radius: 2px;
  
  .notice-bar-content {
    display: flex;
    align-items: center;
    flex: 1;
    
    .notice-bar-icon {
      margin-right: 8px;
      font-size: 14px;
      line-height: 1;
    }
    
    .notice-bar-text {
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      line-height: 1.5715;

      .notice-bar-text-highlight {
        color: rgba(208, 2, 27, 1);
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
  
  .notice-bar-extra {
    display: flex;
    align-items: center;
    margin-left: 16px;

    &-container {
      display: flex;
      align-items: center;
      margin-left: 16px;

      span {
        color: #1890ff;
        cursor: pointer;
      }

      .divider {
        width: 1px;
        height: 12px;
        background-color: rgba(0, 0, 0, 0.15);
        margin: 0 8px;
      }
    }
    
    .notice-bar-close {
      margin-left: 12px;
      margin-bottom: 2px;
      color: rgba(0, 0, 0, 0.45);
      font-size: 20px;
      line-height: 1;
      cursor: pointer;
      transition: color 0.3s;
      
      &:hover {
        color: rgba(0, 0, 0, 0.75);
      }
    }
  }
} 
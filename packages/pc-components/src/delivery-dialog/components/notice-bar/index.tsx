import React from 'react';

interface NoticeBarProps {
  /** 提示文本内容 */
  text: string | React.ReactNode;
  /** 左侧图标（可选） */
  icon?: React.ReactNode;
  /** 右侧操作区域（可选） */
  extra?: React.ReactNode;
  /** 点击关闭回调（可选） */
  onClose?: () => void;
  /** 自定义类名（可选） */
  className?: string;
  /** 自定义样式（可选） */
  style?: React.CSSProperties;
}

const NoticeBar = ({ text, icon, extra, onClose, className = '', style }: NoticeBarProps) => {
  return (
    <div className={`notice-bar ${className}`} style={style}>
      <div className="notice-bar-content">
        {icon && <span className="notice-bar-icon">{icon}</span>}
        <span className="notice-bar-text">{text}</span>
      </div>
      <div className="notice-bar-extra">
        {extra}
        {onClose && (
          <span className="notice-bar-close" onClick={onClose}>
            ×
          </span>
        )}
      </div>
    </div>
  );
};

export default NoticeBar;

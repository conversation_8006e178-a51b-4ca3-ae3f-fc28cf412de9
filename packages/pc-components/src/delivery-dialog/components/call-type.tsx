import { DeliveryCallStrategy } from '@youzan/order-domain-definitions/es/order';
import { BlankLink } from '@youzan/react-components';
import { useLocalStorage } from '@youzan/react-hooks';
import { track } from '@youzan/retail-utils';
import { isRetailSingleStore, isUnifiedShop } from '@youzan/utils-shop';
import { isNil } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';

import { Radio } from 'zent';
import { EXPRESS_CHANNEL_KEY_NAME } from '../constants';
import { makeDeliveryLocalDeliveryPageUrl } from '../utils';

export enum DeliveryCallType {
  /** 呼叫单个服务商，该值限本组件内部使用 */
  Unary = 1,
  /** 同时呼叫多个服务商，值取自后台数据配置，可传出到后端 */
  Parallel = DeliveryCallStrategy.Parallel,
  /** 顺序呼叫多个服务商，值取自后台数据配置，可传出到后端 */
  Sequence = DeliveryCallStrategy.Sequence
}

const AvailableDeliveryCallType = [
  DeliveryCallType.Unary,
  DeliveryCallType.Parallel,
  DeliveryCallType.Sequence
];

export const ParallelOrSequenceDeliveryCallType = [
  DeliveryCallType.Parallel,
  DeliveryCallType.Sequence
] as const;

const DeliveryCallTypeMap = new Map([
  [DeliveryCallStrategy.Parallel, DeliveryCallType.Parallel],
  [DeliveryCallStrategy.Sequence, DeliveryCallType.Sequence]
]);

export const DeliveryCallTypeTextMap = new Map([
  [DeliveryCallType.Parallel, '同时呼叫多个服务商'],
  [DeliveryCallType.Sequence, '顺序呼叫多个服务商']
]);

/** 是否访问过三方同城配送 */
const HasVisitedThirdLocalDeliveryStorageKey =
  'fulfill-express-dialog.has-visited-third-local-delivery';

/**
 * @param {string} desc
 */
function replaceAutoCallDeliveryDesc(desc = '', target = '订单-配送管理') {
  // eslint-disable-next-line no-nested-ternary
  const settingUrl = isUnifiedShop
    ? '/v2/order/delivery'
    : isRetailSingleStore
    ? '/v4/trade/local-delivery'
    : undefined;
  for (let i = 0; i < desc.length; i++) {
    const chars = desc.slice(i, i + target.length);
    if (chars === target) {
      const before = desc.slice(0, i);
      const after = desc.slice(i + target.length);
      return [
        <span key={before}>{before}</span>,
        <a href={settingUrl} key={chars}>
          {chars}
        </a>,
        <span key={after}>{after}</span>
      ];
    }
  }
  return [<React.Fragment key={desc}>{desc}</React.Fragment>];
}

const CallType = function CallType({
  localDeliveryCallConfig = {},
  value,
  warehouseId,
  businessType,
  onChange,
  expressType,
  onRefresh
}: CallTypeProps) {
  const autoCallConfigs = useMemo(
    () => Object.values(localDeliveryCallConfig ?? {}),
    [localDeliveryCallConfig]
  );
  const [type, setType] = useState(DeliveryCallType.Unary);

  const [hasVisitedThirdLocalDelivery, setHasVisitedThirdLocalDelivery] = useLocalStorage(
    HasVisitedThirdLocalDeliveryStorageKey,
    false
  );

  const handleTypeChange = (value?: DeliveryCallType) => {
    if (isNil(value)) return;

    setType(value);
    if (value === DeliveryCallType.Unary) {
      onChange?.({} as Parameters<CallTypeProps['onChange']>[number]);
      return;
    }

    const matchedConfig = autoCallConfigs.find(
      (config) => DeliveryCallTypeMap.get(config.channel) === value
    );
    if (ParallelOrSequenceDeliveryCallType.includes(value) && matchedConfig) {
      onChange?.(matchedConfig);
    }
  };

  // 根据后端配置，预勾选呼叫方式
  useEffect(() => {
    const { channel } = autoCallConfigs.find((config) => config.selected) ?? {};
    const presetSelected = DeliveryCallTypeMap.get(channel as DeliveryCallStrategy);

    let callType = DeliveryCallTypeTextMap.get(presetSelected as DeliveryCallType);
    if (
      ![DeliveryCallType.Parallel, DeliveryCallType.Sequence].includes(
        presetSelected as DeliveryCallType
      ) ||
      !hasVisitedThirdLocalDelivery
    ) {
      callType = '呼叫单个服务商';
    }

    track({
      et: 'click',
      ei: 'open_express_dialog',
      en: '发货打开的默认数据',
      params: {
        component: 'fulfill_express',
        expressType, // 配送方式
        localstorageDeliveryChannel: window.localStorage.getItem(EXPRESS_CHANNEL_KEY_NAME), // 服务商
        callType // 呼叫方式
      },
      pt: ''
    });

    if (!hasVisitedThirdLocalDelivery) {
      /**
       * 第一次访问"呼叫同城配送", 选择"呼叫单个服务商"; 之后按原有逻辑
       * @see https://qima.feishu.cn/wiki/wikcnOvMNFKf9NOz5zdag4NDPJK
       */
      setHasVisitedThirdLocalDelivery(true);
      handleTypeChange(DeliveryCallType.Unary);
      return;
    }

    if (isNil(channel) || isNil(presetSelected)) return;
    setType(presetSelected);

    // 记录送货方式、呼叫方式、快递公司默认选择埋点

    handleTypeChange(presetSelected); // 主动 Change 以触发父级中的价格请求
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 回填逻辑
  useEffect(() => {
    const propChannel = value?.deliveryChannel;
    if (isNil(propChannel)) return;
    if (AvailableDeliveryCallType.includes(propChannel) && propChannel !== type) {
      setType(propChannel);
    }
  }, [type, value]);

  const matchedAutoCallConfig = autoCallConfigs.find(
    (config) => config.channel === value?.deliveryChannel
  );

  return (
    <div className="input-item auto-call">
      <div className="delivery-extra-content__label">呼叫方式：</div>
      <div className="delivery-extra-content__value">
        <Radio.Group
          style={{ marginTop: -3 }}
          value={type}
          onChange={(ev) => handleTypeChange(ev.target?.value)}
        >
          <Radio value={DeliveryCallType.Unary}>呼叫单个服务商</Radio>
          <Radio value={DeliveryCallType.Parallel}>同时呼叫多个服务商</Radio>
          <Radio value={DeliveryCallType.Sequence}>顺序呼叫多个服务商</Radio>
        </Radio.Group>
        {type === DeliveryCallType.Unary && !businessType ? (
          /** 未配置业务类型, 需要跳转配置 */
          <div className="delivery-extra-content__desc">
            配置业务类型后可呼叫
            <BlankLink
              style={{ marginLeft: '8px' }}
              href={makeDeliveryLocalDeliveryPageUrl({
                warehouseId,
                tabType: 'service',
                openDialog: 'business-choice'
              })}
            >
              配置
            </BlankLink>
            {onRefresh ? (
              <BlankLink
                style={{ marginLeft: '12px' }}
                href={window.location.href}
                target="_self"
                onClick={(e) => {
                  e.preventDefault();
                  onRefresh();
                }}
              >
                刷新
              </BlankLink>
            ) : null}
          </div>
        ) : null}

        {ParallelOrSequenceDeliveryCallType.includes(value?.deliveryChannel) &&
          matchedAutoCallConfig && (
            <div className="delivery-extra-content__desc">
              {replaceAutoCallDeliveryDesc(matchedAutoCallConfig.supportDesc)}
            </div>
          )}
      </div>
    </div>
  );
};

export default CallType;

interface DeliveryCallConfig {
  name: string;
  channel: DeliveryCallStrategy;
  initialWeight: number;
  maxWeight: number;
  support: boolean;
  supportCode: 404;
  supportDesc: string;
  selected?: boolean;
}

export interface CallTypeProps {
  localDeliveryCallConfig?: {
    [key: string]: DeliveryCallConfig;
  };
  /** 选中的呼叫方式 */
  value: { deliveryChannel: number };
  warehouseId: number;
  businessType: number;
  onChange(value: DeliveryCallConfig | null): void;
  onRefresh(): void;
  expressType: number;
}

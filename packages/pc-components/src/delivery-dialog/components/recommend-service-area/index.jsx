/* eslint-disable react/react-in-jsx-scope */
import React from 'react';
import { BlankLink } from '@youzan/react-components';
import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Dialog } from 'zent';
import { trackExpressRecommendDialog, trackExpressRecommendTooltip } from '../../track';

function useTrackData(desc = '') {
  const [trackAction, setTrackAction] = useState({});
  const trackDataRef = useRef({
    trackAction,
    setTrackAction,
    promptInfo: 9
  });

  if (desc.includes('预计') && desc.includes(' 可省')) {
    trackDataRef.current.promptInfo = 0;
  } else if (desc.includes('预计')) {
    trackDataRef.current.promptInfo = 1;
  } else if (desc.includes('可省')) {
    trackDataRef.current.promptInfo = 2;
  }

  useEffect(() => {
    trackDataRef.current.trackAction = trackAction;
  }, [trackAction]);

  return trackDataRef;
}

export function RecommendServiceAlert({
  isRecommendStuffShow = false,
  recommendTipVisible,
  recommendationDesc,
  handleGoToUse,
  handleClose
}) {
  const trackDataRef = useTrackData(recommendationDesc);

  const { trackAction, setTrackAction } = trackDataRef.current;
  useEffect(() => {
    const ref = trackDataRef;
    return () => {
      trackExpressRecommendTooltip({
        promptInfo: ref?.current?.promptInfo,
        ...ref?.current?.trackAction
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trackDataRef]);

  return (
    isRecommendStuffShow && (
      <div className="od-recommend-alert">
        <Alert
          style={{ marginTop: 12 }}
          closable
          closed={!recommendTipVisible}
          onClose={() => {
            handleClose();
            setTrackAction({
              ...trackAction,
              clickClose: 1
            });
          }}
        >
          {recommendationDesc}
          <BlankLink
            style={{ float: 'right' }}
            onClick={() => {
              handleGoToUse();
              setTrackAction({
                ...trackAction,
                clickToUse: 1
              });
            }}
          >
            去使用
          </BlankLink>
        </Alert>
      </div>
    )
  );
}

export function RecommendServiceDialog({
  isRecommendStuffShow = false,
  recommendDialogVisible,
  recommendationDesc,
  handleNotRemind,
  handleGoToUse,
  handleClose
}) {
  const trackDataRef = useTrackData(recommendationDesc);

  const { trackAction, setTrackAction } = trackDataRef.current;
  useEffect(() => {
    const ref = trackDataRef;
    return () => {
      trackExpressRecommendDialog({
        promptInfo: ref?.current?.promptInfo,
        ...ref?.current?.trackAction
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [trackDataRef]);

  return isRecommendStuffShow ? (
    <>
      <Dialog
        className="zent-dialog-demo-basic-dialog"
        visible={recommendDialogVisible}
        maskClosable={false}
        onClose={() => {
          handleClose();
          setTrackAction({
            ...trackAction,
            clickClose: 1
          });
        }}
        footer={
          <>
            <Button
              style={{ border: 'none', color: '#979797' }}
              onClick={() => {
                handleNotRemind();
                handleClose();
                setTrackAction({
                  ...trackAction,
                  clickNotRemind: 1
                });
              }}
            >
              不再提醒
            </Button>
            <Button
              onClick={() => {
                handleClose();
                setTrackAction({
                  ...trackAction,
                  clickClose: 1
                });
              }}
            >
              关闭
            </Button>
            <Button
              type="primary"
              onClick={() => {
                handleGoToUse();
                setTrackAction({
                  ...trackAction,
                  clickToUse: 1
                });
              }}
            >
              去使用
            </Button>
          </>
        }
        title="提示"
      >
        <p>{recommendationDesc}</p>
      </Dialog>
    </>
  ) : null;
}

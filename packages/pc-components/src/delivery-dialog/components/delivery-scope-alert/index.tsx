import { isBranchStoreOrFrontWarehouse, isRetailSingleStore } from '@youzan/utils-shop';
import classnames from 'classnames';
import { isNil, isString } from 'lodash';
import React, { useEffect, useState } from 'react';
import { Alert, Icon } from 'zent';
import { queryDeliveryAlertVisibility } from '../../api';

const ScrollToAnchorKey = 'scrollTo';

function createJumpLink(warehouseId: number) {
  // 单店视角
  if (isRetailSingleStore) {
    return `/v4/trade/local-delivery?${ScrollToAnchorKey}=range`;
  }

  // 门店/前置仓视角
  if (isBranchStoreOrFrontWarehouse) {
    return `/v2/order/delivery#/localdelivery?${ScrollToAnchorKey}=range`;
  }

  // 总部视角
  if (!isNil(warehouseId)) {
    return `/v2/order/delivery#/localdelivery/${warehouseId}?${ScrollToAnchorKey}=range`;
  }

  // 兜底
  return '';
}

const DeliveryScopeAlert = function DeliveryScopeAlert({
  orderNo,
  warehouseId
}: DeliveryScopeAlertProps) {
  const [visible, setVisible] = useState(false);
  useEffect(() => {
    (async function query() {
      if (!isString(orderNo)) return;
      const ans = await queryDeliveryAlertVisibility(orderNo);
      setVisible(Boolean(ans));
    })();
  }, [orderNo]);

  if (!visible) return null;
  return (
    <div className={classnames('express-content-item', 'od-delivery-scope-alert')}>
      <Alert closable closeContent={<Icon type="close" />}>
        可获取美团，蜂鸟的配送范围，防止超出配送范围&nbsp;
        <a href={createJumpLink(warehouseId)} target="_blank" rel="noreferrer">
          去设置
        </a>
      </Alert>
    </div>
  );
};

export default DeliveryScopeAlert;

export interface DeliveryScopeAlertProps {
  orderNo: string;
  warehouseId: number;
}

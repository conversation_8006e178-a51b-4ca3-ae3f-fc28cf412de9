/**
 * 发货方式
 *
 * 12, "快递 - 商家呼叫快递"
 * 13, "快递 - 无需物流"
 * 14, "快递 - 电子面单"
 * 21, "同城送 - 商家呼叫三方配送"
 * 22, "同城送 - 商家自主配送"
 */

export const DELIVERY_MODE = {
  expressByMerchant: {
    value: 12,
    text: '自己联系快递',
  },
  withoutExpress: {
    value: 13,
    text: '无需物流',
  },
  expressOnDoor: {
    value: 14,
    text: '在线下单',
  },
  intraCityCall: {
    value: 21,
    text: '呼叫同城配送',
  },
  intraCityByMerchant: {
    value: 22,
    text: '商家自行配送',
  },
  weixinDelivery: {
    value: 15,
    text: '微信物流助手',
  },
  needVerify: {
    value: 1,
    text: '需要验证自提码',
  },
  noNeedVerify: {
    value: 2,
    text: '无需验证自提码',
  },
};
/**
 * 有赞寄件服务意见反馈弹窗组件
 *
 * 功能说明：
 * 1. 提供用户反馈有赞寄件服务不适用原因的收集功能
 * 2. 支持多选预设原因（如：不是自己发货、没有合适的快递公司等）
 * 3. 支持填写其他自定义原因
 * 4. 提交反馈后会自动关闭弹窗
 *
 * 使用示例：
 * ```tsx
 * import { openFeedbackDialog } from './openFeedbackDialog';
 *
 * // 打开反馈弹窗
 * openFeedbackDialog({
 *   onClose: () => {
 *     // 弹窗关闭后的回调
 *   }
 * });
 * ```
 *
 * @param {Object} props - 组件属性
 * @param {Function} props.onClose - 弹窗关闭回调函数
 * @returns {void}
 */

import React, { useState } from 'react';
import { Button, Checkbox, Input, Notify, openDialog, closeDialog } from 'zent';

import * as api from '../../api';

// 常量定义
const MAX_FEEDBACK_LENGTH = 200;
const FEEDBACK_DIALOG_ID = 'feedback-dialog-id';

// 反馈选项
const FEEDBACK_OPTIONS = {
  OTHER: 'other'
} as const;

// 反馈原因列表
const FEEDBACK_REASONS = [
  { value: '0', label: '不是自己发货' },
  { value: '1', label: '没有合适的快递公司或物流产品' },
  { value: '2', label: 'ERP汇总多平台订单发货，管理更便捷' },
  { value: '3', label: '现在的快递价格更低' },
  { value: '4', label: '试用过，体验不好' },
  { value: '5', label: '有兴趣，但还没用' },
  { value: FEEDBACK_OPTIONS.OTHER, label: '其他原因' }
];

const FeedbackForm = ({ onClose }: { onClose?: () => void }) => {
  // ===== State =====
  const [feedback, setFeedback] = useState('');
  const [selectedReason, setSelectedReason] = useState<string[]>([]);

  // ===== Handlers =====
  const handleReasonChange = (values: string[]) => {
    setSelectedReason(values);
  };

  const handleFeedbackChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFeedback(e.target.value);
  };

  // ===== Utils =====
  const getSelectedFeedbackContent = () => {
    const remark = feedback.trim() || '';
    const reason = selectedReason
      .filter((value) => value !== FEEDBACK_OPTIONS.OTHER)
      .map((value) => FEEDBACK_REASONS.find((r) => r.value === value)?.label || '')
      .join(';');
    return { remark, reason };
  };

  const submitFeedback = async () => {
    try {
      const message = getSelectedFeedbackContent();

      await api.addFeedBack({ reasonList: [message] });

      return true;
    } catch (error) {
      console.error('提交反馈失败:', error);
      Notify.error('提交反馈失败，请重试');
      return false;
    }
  };

  const handleSubmit = async () => {
    const result = await submitFeedback();
    if (result) {
      closeDialog(FEEDBACK_DIALOG_ID);
      onClose?.();
    }
  };

  return (
    <div className="feedback-dialog-form">
      <div className="title">方便告诉我们不用有赞寄件的原因吗？</div>
      <Checkbox.Group className="reasons" value={selectedReason} onChange={handleReasonChange}>
        {FEEDBACK_REASONS.map((reason) => (
          <Checkbox
            key={reason.value}
            className={`reason ${selectedReason.includes(reason.value) ? 'checked' : ''}`}
            value={reason.value}
          >
            {reason.label}
          </Checkbox>
        ))}
      </Checkbox.Group>
      {selectedReason.includes(FEEDBACK_OPTIONS.OTHER) && (
        <div className="feedback">
          <div className="feedback-title">你能告诉我们更多吗？</div>
          <Input
            type="textarea"
            value={feedback}
            onChange={handleFeedbackChange}
            placeholder="其他的意见反馈可以在此处填写，你留下的每一条宝贵建议都将帮助我们做得更好。"
            maxLength={MAX_FEEDBACK_LENGTH}
            showCount
          />
        </div>
      )}
      <div className="zent-dialog-r-footer">
        <Button onClick={handleSubmit} type="primary">
          提交反馈
        </Button>
      </div>
    </div>
  );
};

// 意见反馈弹窗
export const openFeedbackDialog = ({ onClose }) => {
  return openDialog({
    dialogId: FEEDBACK_DIALOG_ID,
    title: '意见反馈',
    className: 'feedback-dialog',
    children: <FeedbackForm onClose={onClose} />,
    onClose
  });
};

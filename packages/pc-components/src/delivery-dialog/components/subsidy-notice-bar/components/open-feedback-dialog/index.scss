.feedback {
  &-dialog {
    // 弹窗头部标题
    .zent-dialog-r-header .zent-dialog-r-title {
      font-size: 16px;
      line-height: 24px;
    }

    &-form {
      .title {
        margin: 8px 0 16px;
        font-size: 24px;
        font-weight: 600;
        line-height: 30px;
        margin-bottom: 24px;
        color: #323233;
      }

      .subtitle {
        font-size: 14px;
        font-size: 16px;
        line-height: 22px;
        color: #7d7e80;
        margin-bottom: 20px;
      }

      .reasons {
        display: flex !important;
        flex-direction: column;
        gap: 12px;
      }

      .reason {
        height: 44px;
        border: 1px solid #dcdee0;
        border-radius: 4px;
        padding: 11px !important;
        margin: 0 !important;
        cursor: pointer;
        box-sizing: border-box;

        &:hover {
          border-width: 2px;
          padding: 10px !important;
          border-color: #155bd4;
        }

        &.checked {
          background: #f0f5ff;
          padding: 10px !important;
          border-width: 2px;
          border-color: #155bd4;
        }

        .zent-checkbox-wrap {
          display: flex;
          align-items: center;
          width: 100%;
        }

        .zent-checkbox-label {
          font-weight: 500;
          margin-left: 10px;
          flex: 1;
        }
      }

      .feedback {
        margin-top: 13px;

        &-title {
          font-family: PingFang SC;
          font-size: 12px;
          line-height: 16px;
          color: #323233;
          margin-bottom: 10px;
        }

        .zent-input-wrapper {
          width: 100%;

          textarea {
            height: 96px;
            resize: none;
            padding: 8px;
            font-size: 14px;
            line-height: 20px;
            color: #323233;

            &::placeholder {
              color: #ccc;
            }
          }
        }

        .zent-input-count {
          text-align: right;
          margin-top: 4px;
          font-size: 12px;
          line-height: 16px;
          color: #969799;
        }
      }

      // 底部按钮区域
      .zent-dialog-r-footer {
        margin-top: 10px;
        display: flex;
        justify-content: end;
      }
    }
  }
}

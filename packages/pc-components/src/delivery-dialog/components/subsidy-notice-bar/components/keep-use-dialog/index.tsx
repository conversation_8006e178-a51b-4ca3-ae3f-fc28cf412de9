/**
 * 有赞寄件挽留弹窗组件
 *
 * 功能说明：
 * 1. 当用户选择不使用有赞寄件时，弹出此弹窗进行挽留
 * 2. 展示有赞寄件的核心优势：
 *    - 开通立即可用，无需找快递网点谈价签约
 *    - 运费折扣+补贴（顺丰官网7折、京东4.5元起、申通2.5元起）
 *    - 一键取号发货，3秒完成
 *    - 不限最低单量，一单也优惠
 *    - 先发货后结算，无需预充值
 * 3. 提供轮播图展示开通服务流程
 * 4. 用户可选择"立即体验"或"我不需要"
 * 5. 选择"我不需要"会跳转到意见反馈弹窗
 *
 * 使用示例：
 * ```tsx
 * import { opneKeepUseDialog } from './openKeepUseDialog';
 *
 * // 打开挽留弹窗
 * opneKeepUseDialog({
 *   toUse: () => {
 *     // 用户选择立即体验的回调
 *   },
 *   setHasShown: () => {
 *     // 设置已显示标记的回调
 *   }
 * });
 * ```
 *
 * @param {Object} props - 组件属性
 * @param {Function} props.toUse - 用户选择立即体验的回调函数
 * @param {Function} props.setHasShown - 设置已显示标记的回调函数
 * @returns {void}
 */

import React from 'react';
import { openDialog, closeDialog, Swiper, Button } from 'zent';
import { track } from '@youzan/retail-utils';

import { openFeedbackDialog } from '../open-feedback-dialog';

const KEEP_USE_DIALOG_ID = 'keep-use-dialog-id';

const openKeepUseDialog = ({
  toUse,
  setHasShown
}: {
  toUse: () => void;
  setHasShown: () => void;
}) => {
  const onClose = () => {
    closeDialog(KEEP_USE_DIALOG_ID);
    track({
      et: 'click', // 事件类型
      ei: 'keep_use_dialog_close_click', // 事件标识
      en: '我不需要按钮点击', // 事件名称
      params: {
        component: 'keep_use_dialog'
      } // 事件参数
    });
    openFeedbackDialog({
      onClose: () => {
        setHasShown();
      }
    });
  };

  return openDialog({
    dialogId: KEEP_USE_DIALOG_ID,
    title: '经营建议',
    children: (
      <div className="keep-use-dialog">
        <div className="keep-use-dialog-text">
          <div className="keep-use-dialog-text-title">
            <p>有赞寄件希望为你提供</p>
            <p style={{ fontSize: 28, color: '#155BD4' }}>更优惠、更高效的发货方式</p>
            <p>真的不试试吗？</p>
          </div>
          <div className="keep-use-dialog-text-content">
            <p>
              <span>开通立即可用</span>无需找快递网点谈价签约
            </p>
            <p>
              <span>运费折扣+补贴</span>顺丰官网7折 | 京东4.5元起 | 申通2.5元起
            </p>
            <p>
              <span>一键取号发货</span>3秒完成发货，还可以批量操作哦
            </p>
            <p>
              <span>不限最低单量</span>一单也是优惠价，一单也上门取件
            </p>
            <p>
              <span>先发货后结算</span>无需预充值，揽收后扣费，运费一目了然
            </p>
          </div>
        </div>
        <div className="keep-use-dialog-img-list">
          <Swiper className="keep-use-dialog-img-list-carousel" dotsColor="blue" autoplay>
            {['订单发货', '选择有赞寄件', '发货成功'].map((title, index) => (
              <div key={index}>
                <div className="keep-use-dialog-img-list-title">{title}</div>
                <img
                  src={`https://b.yzcdn.cn/order/postage-product-info/youzanjijian_v2-${index}.png`}
                  alt="如何使用有赞寄件"
                />
              </div>
            ))}
          </Swiper>
        </div>
      </div>
    ),
    footer: (
      <div className="keep-use-dialog-footer">
        <Button onClick={onClose}>我不需要</Button>
        <Button
          type="primary"
          onClick={() => {
            closeDialog(KEEP_USE_DIALOG_ID);
            toUse();
            track({
              et: 'click', // 事件类型
              ei: 'to_use_click', // 事件标识
              en: '立即体验点击', // 事件名称
              params: {
                component: 'keep_use_dialog'
              } // 事件参数
            });
          }}
        >
          立即体验
        </Button>
      </div>
    ),
    onClose: () => closeDialog(KEEP_USE_DIALOG_ID)
  });
};

export default openKeepUseDialog;

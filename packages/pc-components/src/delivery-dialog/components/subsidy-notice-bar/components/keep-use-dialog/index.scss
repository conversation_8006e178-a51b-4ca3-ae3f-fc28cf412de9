.keep-use-dialog {
  display: flex;


  &-text {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-right: 50px;
    padding: 44px 30px;
    padding-bottom: 0;

    &-title {
      font-size: 22px;
      font-weight: 500;
      margin-bottom: 50px;

      p {
        margin-bottom: 20px;
      }
    }

    &-content {
      font-size: 14px;
      color: #999;
      
      p {
        margin-bottom: 18px;

        span {
          font-weight: 500;
          color: #333;
          margin-right: 8px;
          &::before {
            content: '•';
            margin-right: 8px;
            color: #6582B2;
            font-size: 20px;
          }
        }
      }
    }
    
  }

  &-img-list {
    margin-right: 30px;
    margin-top: 10px;

    &-title {
      color: #999;
      text-align: right;
      margin-bottom: 10px;
      font-size: 12px;

      // 绘制倒着的实体三角形 
      &::before {
        content: '';
        display: inline-block;
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-bottom: 5px solid transparent;
        border-left: 8px solid #999;
        margin-right: 5px;
        transform: rotate(90deg);
      }
    }

    &-carousel {
      width: 490px;
      height: 430px;

      img {
        height: 365px;
        width: 100%;
      }
    }
  }
}
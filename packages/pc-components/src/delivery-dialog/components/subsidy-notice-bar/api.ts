import ajax from 'zan-pc-ajax';

// 添加反馈
export function addFeedBack(data) {
  const url = '/v2/order/addFeedBack.json';
  return ajax({
    url,
    method: 'POST',
    contentType: 'application/json',
    data
  });
}

// 获取店铺补贴权益
export function getShopRights() {
  return ajax({
    url: '/v4/trade/order/getShopRights.json',
    method: 'GET'
  });
}

// 检查是否在apollo配置的白名单中
export function checkApolloConfig(data) {
  const url = '/v4/trade/common/checkApolloConfig.json';
  return ajax({
    url,
    method: 'GET',
    data
  });
}

// 检查是否在有赞寄件黑名单中
export function checkIsYZShoppingBlackList() {
  const url = '/v4/trade/api/express/checkIsYZShoppingBlackList.json';
  return ajax({
    url,
    method: 'GET'
  });
}

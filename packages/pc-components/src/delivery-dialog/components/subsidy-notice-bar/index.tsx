/**
 * 有赞寄件补贴通知栏组件
 *
 * 功能说明：
 * 1. 显示有赞寄件服务的补贴信息（如：首单补贴、新用户补贴等）
 * 2. 提供"立即使用"和"继续使用"两个操作按钮
 * 3. 支持用户反馈不适用原因
 * 4. 支持关闭通知栏
 *
 * 使用示例：
 * ```tsx
 * import { SubsidyNoticeBar } from './SubsidyNoticeBar';
 *
 * // 在组件中使用
 * <SubsidyNoticeBar
 *   onChangeExpress={() => {
 *     // 切换快递公司后的回调
 *   }}
 * />
 * ```
 *
 * @param {Object} props - 组件属性
 * @param {Function} props.onChangeExpress - 切换快递公司后的回调函数
 * @returns {JSX.Element} 补贴通知栏组件
 */

import React, { useEffect, useState } from 'react';
import { openDialog, closeDialog } from 'zent';
import { global, formatDate, track } from '@youzan/retail-utils';

import NoticeBar from '../notice-bar';
import openKeepUseDialog from './components/keep-use-dialog';

import * as api from './api';
import { DELIVERY_MODE } from './constants';

interface SubsidyNoticeBarProps {
  onChangeExpress: (val: number) => void;
}

const HowToUseDialogId = 'how-to-use-dialog-id';
const { KDT_ID, USER_INFO } = global;
const StorageKey = `${KDT_ID}_${USER_INFO.id}_subsidy_notice_bar_last_shown`;

const SubsidyNoticeBar = ({ onChangeExpress }: SubsidyNoticeBarProps) => {
  const [isShowRightTips, setIsShowRightTips] = useState(false);
  const [shopRights, setShopRights] = useState<any>({});
  const [isHasCache, setIsHasCache] = useState(false);
  const [isShowRights, setIsShowRights] = useState(false);

  useEffect(() => {
    // 检查是否在免打扰的白名单内、是否有赞寄件黑名单
    Promise.all([
      // 检查是否在免打扰白名单中
      api.checkApolloConfig({
        key: 'YZ_shopping_no_request'
      }),
      // 检查是否在有赞寄件黑名单中
      api.checkIsYZShoppingBlackList()
    ])
      .then(([isCheckApolloConfig, isCheckYZShoppingBlackList]) => {
        // 如果任一是true，那就不显示tips
        if (isCheckApolloConfig || isCheckYZShoppingBlackList) {
          setIsShowRightTips(false);
        } else {
          // 如果不在免打扰的白名单且不在有赞寄件黑名单中，则获取店铺补贴权益
          api.getShopRights().then((res) => {
            setIsShowRightTips(true);
            const { endTime, totalAmount } = (res?.rights || [])[0] || {};
            const isShowRights = totalAmount && endTime;
            setShopRights({ endTime, totalAmount });
            setIsShowRights(isShowRights);
            track({
              et: 'view', // 事件类型
              ei: 'subsidy_notice_bar_show', // 事件标识
              en: '有赞寄件补贴提示小黄条曝光', // 事件名称
              params: {
                has_subsidy: isShowRights ? '1' : '0',
                component: 'subsidy_notice_bar'
              } // 事件参数
            });
          });
        }
      })
      .catch(() => {
        setIsShowRightTips(false);
      });
    const isHasCache = localStorage.getItem(StorageKey);
    setIsHasCache(!!isHasCache);
  }, []);

  const setHasShown = () => {
    localStorage.setItem(StorageKey, new Date().getTime().toString());
    setIsHasCache(true);
  };

  const toUse = () => {
    onChangeExpress(DELIVERY_MODE.expressOnDoor.value);
  };

  const handleToUseClick = () => {
    track({
      et: 'click', // 事件类型
      ei: 'to_use_click', // 事件标识
      en: '立即体验点击', // 事件名称
      params: {
        has_subsidy: isShowRights ? '1' : '0',
        component: 'subsidy_notice_bar'
      } // 事件参数
    });
    toUse();
  };

  // 免打扰是最高优先级
  if (!isShowRightTips) {
    return null;
  }

  // 如果缓存中没有，或者有补贴权益，则显示提示条
  // 有运费补贴，则一直展示小黄条
  if (!isHasCache || isShowRights) {
    const options = {
      ...(!isShowRights && {
        onClose: () => {
          track({
            et: 'view', // 事件类型
            ei: 'keep_use_dialog_show', // 事件标识
            en: '有赞寄件挽留弹窗曝光', // 事件名称
            params: {
              component: 'keep_use_dialog'
            } // 事件参数
          });
          openKeepUseDialog({ toUse, setHasShown });
        }
      })
    };

    return (
      <NoticeBar
        text={
          isShowRights ? (
            <>
              你有
              <span className="notice-bar-text-highlight">{shopRights.totalAmount / 100}元</span>
              有赞寄件开店礼包补贴将于{formatDate(shopRights.endTime, 'YYYY-MM-DD')}
              过期，补贴后运费低至
              <span className="notice-bar-text-highlight">0.5元</span>/单。
            </>
          ) : (
            <>
              用有赞寄件，一键取号发货，运费低至
              <span className="notice-bar-text-highlight">2.5元</span>，一单也是优惠价！
            </>
          )
        }
        extra={
          <div className="notice-bar-extra-container">
            <span onClick={handleToUseClick}>立即体验</span>
            <div className="divider" />
            <span
              onClick={() => {
                track({
                  et: 'click', // 事件类型
                  ei: 'how_to_use_click', // 事件标识
                  en: '如何使用点击', // 事件名称
                  params: {
                    has_subsidy: isShowRights ? '1' : '0',
                    component: 'subsidy_notice_bar'
                  } // 事件参数
                });
                openDialog({
                  dialogId: HowToUseDialogId,
                  title: '如何使用有赞寄件',
                  children: (
                    <div style={{ fontSize: 16 }}>
                      <p style={{ marginBottom: 10 }}>
                        1、快递订单发货时，发货方式选择【在线下单】
                      </p>
                      <p style={{ marginBottom: 10 }}>2、选择带【有赞寄件】标签的快递公司</p>
                      <p style={{ marginBottom: 16 }}>3、填写发货地址等信息后点击【发货】即可</p>
                      <img
                        style={{ width: 760, height: 618 }}
                        src={
                          isShowRights
                            ? 'https://b.yzcdn.cn/order/postage-product-info/subsidy-info-v2.png'
                            : 'https://b.yzcdn.cn/order/postage-product-info/no-subsidy-info-v2.png'
                        }
                        alt="如何使用有赞寄件"
                      />
                    </div>
                  ),
                  onClose: () => closeDialog(HowToUseDialogId)
                });
              }}
            >
              如何使用
            </span>
          </div>
        }
        style={{ margin: '10px 0' }}
        {...options}
      />
    );
  }

  return null;
};

export default SubsidyNoticeBar;

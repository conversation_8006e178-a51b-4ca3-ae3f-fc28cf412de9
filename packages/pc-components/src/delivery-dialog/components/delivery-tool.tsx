import { PopInfo } from '@youzan/retail-components';
import React from 'react';
import { Radio, RadioGroup } from 'zent';

enum DeliveryToolEnum {
  // 电动车
  ElectricBike = 0,
  // 小汽车
  SmallCar = 1
}

const DeliveryToolTypes = new Map<DeliveryToolEnum, string>([
  [DeliveryToolEnum.ElectricBike, '电动车'],
  [DeliveryToolEnum.SmallCar, '小汽车']
]);

const DeliveryTool = ({ onChange, value }: { onChange: (state: any) => void; value: number }) => {
  return (
    <div className="express-content-item">
      <div className="express-content-item__label">交通工具：</div>
      <RadioGroup
        value={value}
        onChange={({ target: { value } }) => {
          onChange(value);
        }}
      >
        {Array.from(DeliveryToolTypes).map(([id, name]) => (
          <Radio value={id} key={id}>
            {name}
          </Radio>
        ))}
      </RadioGroup>
      <PopInfo
        className="delivery-extra-content__value__pop-info"
        popContent="骑手驾驶小轿车派送,价格可能高于电动车配送。"
      />
    </div>
  );
};

export default DeliveryTool;

@import '../../../../style';

.od-label-line {
  display: flex;
  margin-bottom: 15px;
  align-items: center;

  .od-label-line {
    &__label {
      width: 75px;
      margin-right: 25px;
      color: $color-text-primary;
      text-align: right;
    }

    &__component {
      min-width: 180px;

      .zent-select,
      .zent-input {
        width: 180px;
      }
    }
  }
}

.od-express-self-inputs {
  .action-item {
    color: $color-link;
    cursor: pointer;

    &.disabled {
      color: $color-text-secondary;
      cursor: not-allowed;
    }
  }

  .single-pack {
    &__input-action {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: $font-size-small;

      .goods-num {
        display: flex;
        align-items: center;

        &__input {
          width: 100px;
          margin: 0 5px;
        }
      }
    }
  }

  .express-self-tips {
    padding-left: 100px;
    font-size: $font-size-small;
    color: $color-text-secondary;
    margin-top: 15px;
  }
}

.od-tip-actions {
  padding-left: 90px;
  font-size: $font-size-small;

  .express-multi__actions .action-item {
    margin-right: 60px;
  }
}

import { NumberInput } from '@youzan/retail-components';
import cx from 'classnames';
import { get } from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';
import { Input, Notify, Pop } from 'zent';
import FetchSelect from '../../../fetch-select';
import { fetchExpressCompany } from '../../api';

function LabelLine({ label, children }) {
  return (
    <div className="od-label-line">
      <div className="od-label-line__label">{label}：</div>
      <div className="od-label-line__component">{children}</div>
    </div>
  );
}

const ExpressCompanySelect = (props) => {
  const fetchAjax = () =>
    fetchExpressCompany()
      .then(({ allExpress = [] }) => allExpress.filter((item) => item.display))
      .catch((err) => {
        Notify.error(err.msg || '获取物流公司列表失败！');
      });

  return (
    <FetchSelect
      fetchAjax={fetchAjax}
      optionValue="id"
      placeholder="请选择物流公司"
      autoWidth
      optionText="name"
      filter={(item, keyword) => item.name.includes(keyword)}
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...props}
    />
  );
};

class ExpressSelfInputs extends React.Component {
  static propTypes = {
    value: PropTypes.array,
    onChange: PropTypes.func
  };

  static defaultProps = {
    value: [{}]
  };

  onClearAll = () => {
    this.props.onChange([{}]);
  };

  onPackChange = (packIdx, changeInfo) => {
    const { value, onChange } = this.props;

    const displayValue = value.length > 0 ? value : [{}];
    const newValue = displayValue.map((prevPack, idx) => {
      if (idx === packIdx) {
        return { ...prevPack, ...changeInfo };
      }
      return prevPack;
    });
    onChange(newValue);
  };

  onAddPack = () => {
    const { value, onChange } = this.props;
    const increasedPacks = value.length === 0 ? [{}, {}] : [{}];

    onChange([...value, ...increasedPacks]);
  };

  onDeletePack = (packIdx) => {
    const { value, onChange } = this.props;
    const newValue = value.filter((_, idx) => idx !== packIdx);
    onChange(newValue);
  };

  createChangeHandler = (key, packIdx) => (e) =>
    this.onPackChange(packIdx, { [key]: get(e, 'target.value', e) });

  renderSingleInput = ({ expressNo = '', expressId = '', num = '' }, packIdx) => {
    const { value } = this.props;

    const showMultiInput = value.length > 1;

    return (
      <div key={packIdx} className="single-pack">
        <LabelLine label="物流公司">
          <ExpressCompanySelect
            value={expressId}
            onChange={this.createChangeHandler('expressId', packIdx)}
          />
        </LabelLine>
        <LabelLine label="物流单号">
          <Input value={expressNo} onChange={this.createChangeHandler('expressNo', packIdx)} />
        </LabelLine>
        {showMultiInput && (
          <LabelLine label={`包裹${packIdx + 1}`}>
            <div className="single-pack__input-action">
              <div className="goods-num">
                共计
                <NumberInput
                  showStepper
                  min={1}
                  decimal={0}
                  value={num}
                  className="goods-num__input"
                  onChange={this.createChangeHandler('num', packIdx)}
                />
                件
              </div>
              <span onClick={() => this.onDeletePack(packIdx)} className="action-item">
                删除
              </span>
            </div>
          </LabelLine>
        )}
      </div>
    );
  };

  renderTipActions = () => {
    const { value, disableMultiDesc, disableMulti } = this.props;

    if (value.length <= 1) {
      return (
        <div className={cx('od-tip-actions', 'express-normal')}>
          一种商品按数量拆分多包裹发送(仅针对一种单品)
          {disableMulti ? (
            <Pop trigger="hover" content={disableMultiDesc}>
              <span className="action-item disabled">新增运单</span>
            </Pop>
          ) : (
            <span className="action-item" onClick={this.onAddPack}>
              新增运单
            </span>
          )}
        </div>
      );
    }

    return (
      <div className={cx('od-tip-actions', 'express-multi')}>
        <div className="express-multi__actions">
          <span className="action-item" onClick={this.onClearAll}>
            全部清空
          </span>
          <span className="action-item" onClick={this.onAddPack}>
            继续增加
          </span>
        </div>
        每个包裹必须填写商品数量且各包裹数量总和等于该商品总数量才可发货
      </div>
    );
  };

  render() {
    const { value, multiExpress } = this.props;

    const displayValues = value.length > 0 ? value : [{}];
    return (
      <div className="od-express-self-inputs">
        {displayValues.map(this.renderSingleInput)}
        {multiExpress && this.renderTipActions()}
        <p className="express-self-tips">
          请仔细填写物流公司及快递单号，发货后72小时内仅支持做一次更正，逾期不可修改
        </p>
      </div>
    );
  }
}

export default ExpressSelfInputs;

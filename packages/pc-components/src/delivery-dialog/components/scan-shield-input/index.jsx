import { NumberInput } from '@youzan/retail-components';
import { Form } from '@zent/compat';
import cx from 'classnames';
import { omit } from 'lodash';
import React from 'react';

import { getGoodsScanHandle } from '../goods-scan-entry';

const { unknownProps } = Form;

let keyDownCacheValue = [];
const getKeyDownHandle = getGoodsScanHandle();

/**
 * 扫码枪扫码时产生连续的keydown事件，如果鼠标焦点在input输入框中，条码会直接输入；
 * 这里屏蔽直接扫码录入的值。
 */
const ScanShieldInput = React.forwardRef((props, ref) => {
  const { desc, displayError, error } = props;
  const restProps = omit(props, ['onKeyDown', 'desc', ...unknownProps]);
  const onKeyDownHandle = getKeyDownHandle(
    () => {
      keyDownCacheValue.push(restProps.value);
    },
    () => {
      restProps.onChange(keyDownCacheValue[0]);
      keyDownCacheValue = [];
    },
    () => {
      keyDownCacheValue = [];
    }
  );

  // 配合zent Field进行错误信息展示，displayError为true一定展示错误信息，表单在初始状态提交会设置isDirty为true
  const showError =
    displayError === undefined ? props.isDirty && props.error !== null : displayError;
  const groupClassName = cx({
    'od-scan-shield-input': true,
    'has-error': showError
  });

  return (
    <div className={groupClassName}>
      <NumberInput onKeyDown={onKeyDownHandle} {...restProps} ref={ref} />
      <div className={cx('input-desc', { 'input-desc-error': showError })}>
        {showError ? error : desc}
      </div>
    </div>
  );
});

export default ScanShieldInput;

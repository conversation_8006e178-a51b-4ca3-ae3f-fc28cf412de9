import { ExpressType } from '@youzan/order-domain-definitions';
import { BlankLink } from '@youzan/react-components';
import { setUrlDomain, global } from '@youzan/retail-utils';
import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Icon, Pop, Radio, Tag, Alert } from 'zent';

import {
  NO_EXPRESS,
  THIRD_LOCAL_DELIVERY,
  WX_EXPRESS,
  WX_ORDER_MARK,
  EXPRESS_SYSTEM_CALL,
  EXPRESS_SELF
} from '../constants';

import { WaybillVersionEnum } from '../types';

import { trackSelectingExpressType } from '../track';
import SubsidyNoticeBar from './subsidy-notice-bar';

import * as api from '../api';

const WEIXIN_DELIVERY_HELPER = '/v4/trade/wechat-delivery-helper';
const RadioGroup = Radio.Group;

const ExpressTypes = ({
  deliveryTypes = [],
  expressType,
  value: current,
  onChange,
  orderMark,
  thirdDeliveryPromptConfig = {},
  waybillVersion,
  isRemained,
  onChangeIsRemained
}) => {
  const [alertShow, setAlertShow] = useState(false);
  const [remindInfo, setRemindInfo] = useState({});
  const [newElectronWayBillTipConfig, setNewElectronWayBillTipConfig] = useState({});

  const timer = useRef(null);

  useEffect(() => {
    if (newElectronWayBillTipConfig.remindKey) {
      api
        .getRemindInfo({
          remindType: newElectronWayBillTipConfig.remindKey
        })
        .then((res) => {
          setRemindInfo(res || {});
        });
    }
  }, [newElectronWayBillTipConfig.remindKey]);

  const handleGetApolloConfig = useCallback(() => {
    if (waybillVersion === WaybillVersionEnum.Old) {
      api.getNewElectronWayBillTipConfig().then((res = {}) => {
        setNewElectronWayBillTipConfig(res);
        global.BUSINESS.newElectronWayBillTipConfig = res;
      });
    }
  }, [waybillVersion]);

  useEffect(() => {
    if (!global.BUSINESS.newElectronWayBillTipConfig) {
      waybillVersion === WaybillVersionEnum.Old && handleGetApolloConfig();
    } else {
      setNewElectronWayBillTipConfig(global.BUSINESS.newElectronWayBillTipConfig || {});
    }
  }, [handleGetApolloConfig, waybillVersion]);

  const handleSetRemindInfo = useCallback(
    async (isOver = 0) => {
      if (newElectronWayBillTipConfig.remindKey) {
        await api.setRemindInfo({
          remindType: newElectronWayBillTipConfig.remindKey,
          isOver
        });
        onChangeIsRemained();
      }
    },
    [newElectronWayBillTipConfig, onChangeIsRemained]
  );

  useEffect(() => {
    if (timer.current) clearTimeout(timer.current);
    const show = !!(
      waybillVersion === WaybillVersionEnum.Old &&
      current === EXPRESS_SYSTEM_CALL &&
      newElectronWayBillTipConfig?.tip &&
      remindInfo.remindType &&
      !(remindInfo.isOver || remindInfo?.remindCount >= newElectronWayBillTipConfig.maxShowCount)
    );
    setAlertShow(show);
    if (show) {
      /** 曝光2秒算一次 */
      timer.current = setTimeout(() => {
        if (alertShow && !isRemained) {
          handleSetRemindInfo();
        }
      }, 2000);
    }
  }, [
    waybillVersion,
    current,
    alertShow,
    newElectronWayBillTipConfig,
    handleSetRemindInfo,
    remindInfo,
    isRemained
  ]);

  const renderRightsTips = (deliveryTypes) => {
    // 1. 当前是快递发货场景
    // 2. 选择的是自己联系物流方式
    // 3. 当前有在线下单选项
    // 4. 新版电子面单
    // 5. 有可用的有赞寄件服务商
    if (
      expressType === ExpressType.Express &&
      deliveryTypes.find((item) => item.id === EXPRESS_SYSTEM_CALL) &&
      current === EXPRESS_SELF &&
      waybillVersion === WaybillVersionEnum.New &&
      (window._global?.expressCompanies || []).find(item => item.recommend)
    ) {
      return <SubsidyNoticeBar onChangeExpress={(val) => onChange(val)} />;
    }
    return null;
  };

  return (
    <>
      <div className="express-content-item" style={{ display: 'block' }}>
        <div style={{ display: 'flex' }}>
          <div className="express-content-item__label">发货方式：</div>
          {deliveryTypes.length > 0 && (
            <RadioGroup
              value={current}
              onChange={({ target: { value } }) => {
                trackSelectingExpressType({
                  expressType: value
                });
                onChange(value);
              }}
            >
              {deliveryTypes.map(({ id, text }) => {
                const radioEle = (
                  <Radio value={id} key={id}>
                    {text}
                    {id === THIRD_LOCAL_DELIVERY && thirdDeliveryPromptConfig.message ? (
                      <>
                        <Tag theme="blue" outline className="city-delivery-text">
                          {thirdDeliveryPromptConfig.message}
                        </Tag>
                      </>
                    ) : (
                      ''
                    )}
                  </Radio>
                );
                // 快递发货无需物流 给个气泡提示
                if (expressType === ExpressType.Express && id === NO_EXPRESS) {
                  return (
                    <Pop
                      trigger="hover"
                      position="top-left"
                      content="选择无需物流后无法更改发货方式，请慎重选择"
                      wrapperClassName="express-type__item"
                      key={id}
                    >
                      {radioEle}
                    </Pop>
                  );
                }
                // 快递发货微信物流 给个气泡提示
                if (expressType === ExpressType.Express && id === WX_EXPRESS) {
                  const isWxShop = orderMark === WX_ORDER_MARK; // 是否微信小程序订单（仅微信小程序订单支持微信物流）
                  return (
                    <Radio value={id} disabled={!isWxShop} key={id}>
                      <Pop
                        wrapperClassName="express-type__item"
                        trigger="hover"
                        content={
                          <div style={{ width: 280 }}>
                            目前仅支持微信小程序订单使用，更多渠道订单正在接入中，敬请期待…
                          </div>
                        }
                      >
                        <span>{text}</span>
                      </Pop>
                      <Pop
                        trigger="hover"
                        content={
                          <div style={{ width: 280 }}>
                            <ul>
                              <li>· 支持市面主流快递公司在线下单；</li>
                              <li>
                                ·
                                接入微信官方物流通知，帮助消费者快速找到订单，还能提升15%店铺回流；
                              </li>
                            </ul>
                            <BlankLink href={WEIXIN_DELIVERY_HELPER}>立即使用</BlankLink>
                          </div>
                        }
                        position="top-center"
                      >
                        <Icon
                          style={{
                            color: '#c8c9cc',
                            marginLeft: 5,
                            fontSize: 16,
                            verticalAlign: 'middle'
                          }}
                          type="help-circle"
                        />
                      </Pop>
                    </Radio>
                  );
                }
                return radioEle;
              })}
            </RadioGroup>
          )}
        </div>
        {/* 补贴权益提示 */}
        {renderRightsTips(deliveryTypes)}
      </div>
      {alertShow && (
        <Alert className="od-new-electron-waybill-alert" type="warning">
          {newElectronWayBillTipConfig.tip}
          {newElectronWayBillTipConfig.bbsUrl && (
            <BlankLink
              href={setUrlDomain(newElectronWayBillTipConfig.bbsUrl, 'help')}
              onClick={() => handleSetRemindInfo(1)}
            >
              查看详情
            </BlankLink>
          )}
        </Alert>
      )}
    </>
  );
};

export default ExpressTypes;

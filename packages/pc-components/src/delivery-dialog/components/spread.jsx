import { formatMoneyRound } from '@youzan/order-domain-utils';
import React from 'react';

const Spread = ({ spread }) => {
  return spread ? (
    <div className="express-content-item spread">
      <div className="express-content-item__label">共退差价：</div>
      <div className="express-content-item__value spread-price">¥{formatMoneyRound(spread)}</div>
    </div>
  ) : null;
};

export default Spread;

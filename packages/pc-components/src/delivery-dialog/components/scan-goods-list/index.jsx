/* eslint-disable react/jsx-props-no-spreading */
import { ExpressType } from '@youzan/order-domain-definitions';
import { compressBy } from '@youzan/order-domain-utils';
import { Form } from '@zent/compat';
import cx from 'classnames';
import { pick, sortBy, toNumber } from 'lodash';
import React from 'react';
import { Button, Grid, NumberInput } from 'zent';
import GoodsInfo from '../../../goods-info';
import { LotCodeSelectorDialogTrigger } from '../../../lot-code-selector/dialog-trigger';

import ScanShieldInput from '../scan-shield-input';

const { Field } = Form;
const { SelfFetch } = ExpressType;

const spreadDesc = (spread) => (
  <p className="spread-content">
    <span className="spread-info">
      实发重量：{spread?.formatRealWeight ? `${spread.formatRealWeight}kg` : '--'}
    </span>
    <span className="spread-info">
      重量差值：{spread?.weightDiff ? `${spread.weightDiff}kg` : '--'}
    </span>
    <span className="spread-info spread-price">
      应退差价：{spread?.spreadDiff ? `¥${spread.spreadDiff}` : '--'}
    </span>
  </p>
);

const RealWeightEntry = ({ onGoodsSpreadsChange, goods, spreads, indent }) => {
  const entryLength = Math.min(50, goods.goodsNum);
  return (
    <div className={cx('express-table-expanded', { indent })}>
      <Form className="express-table-expanded__content">
        {Array.from({ length: entryLength }).map((_, index) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <div className="express-table-expanded__item" key={index}>
              <Field
                component={ScanShieldInput}
                name={`${goods.itemIdStr}-${index}`}
                placeholder="请输入实发重量"
                addonAfter="kg"
                decimal={3}
                max={999.999}
                min={0.001}
                onChange={(e, value) => {
                  onGoodsSpreadsChange(goods.itemIdStr, index, value);
                }}
                validateOnBlur={false}
                validations={{
                  format(values, value) {
                    return toNumber(value) > 0;
                  }
                }}
                validationErrors={{
                  format: '请输入实际重量'
                }}
                desc={spreadDesc(spreads[index])}
              />
            </div>
          );
        })}
      </Form>
    </div>
  );
};

export default function ScanGoodsList(props) {
  const {
    data,
    updateScanItemList,
    selectedItems,
    expressType,
    goodsSpreads,
    orderNo,
    selectedLotCodeDataByItemId,
    onSelectedLotCodeDataByItemIdChange,
    onGoodsSpreadsChange
  } = props;

  const goodsColumns = [
    {
      title: '商品',
      width: '35%',
      bodyRender: (goodsInfo) => <GoodsInfo {...goodsInfo} className="od-dialog-goods-info" />
    },
    {
      title: '已扫数量',
      width: '35%',
      bodyRender: ({
        itemIdStr,
        scanGoodsNum,
        goodsNum,
        skuCode,
        goodsId,
        skuId,
        isLotCodeItem,
        deliveryStatus,
        kdtId: orderKdtId
      }) => {
        const hasError = scanGoodsNum !== goodsNum;
        const toDelivery = deliveryStatus === 0;

        return (
          <div className="od-scan-num-input-wrapper">
            <NumberInput
              className={hasError ? 'error' : ''}
              value={scanGoodsNum}
              min={0}
              max={goodsNum}
              integer
              placeholder="已扫数量"
              onChange={(v) => {
                if (v === scanGoodsNum) {
                  // 数量一样不触发更新
                  return;
                }
                const newScanItemList = [...data];
                const target = newScanItemList.find((v) => v.skuCode === skuCode);
                target.scanGoodsNum = v;
                updateScanItemList(newScanItemList);
              }}
            />
            {hasError && <p className="help-error">已扫数量需要等于总发货量</p>}
            {isLotCodeItem && (
              <LotCodeSelectorDialogTrigger
                goodsId={goodsId}
                skuId={skuId}
                orderNo={orderNo}
                orderKdtId={orderKdtId}
                orderItemId={itemIdStr}
                value={selectedLotCodeDataByItemId[itemIdStr]}
                max={scanGoodsNum}
                disabled={!toDelivery}
                onChange={(value) =>
                  onSelectedLotCodeDataByItemIdChange({
                    ...selectedLotCodeDataByItemId,
                    [itemIdStr]: value
                  })
                }
              >
                <span className={cx('goods-lot-code', { enabled: toDelivery })}>
                  选择批号(
                  {(selectedLotCodeDataByItemId[itemIdStr]?.selectedLotCodes || []).length})
                </span>
              </LotCodeSelectorDialogTrigger>
            )}
          </div>
        );
      }
    },
    {
      title: '总发货量',
      width: '15%',
      bodyRender: ({ goodsNum }) => {
        return <p>{goodsNum}</p>;
      }
    },
    {
      title: '操作',
      width: '15%',
      textAlign: 'right',
      bodyRender: ({ skuCode }) => {
        return (
          <Button
            type="primary"
            onClick={() => {
              const newScanItemList = [...data];
              const targetIdx = newScanItemList.findIndex((v) => v.skuCode === skuCode);
              newScanItemList.splice(targetIdx, 1);
              updateScanItemList(newScanItemList);
            }}
          >
            移除
          </Button>
        );
      }
    }
  ];

  const isSelfFetch = expressType === SelfFetch;

  const goodsList = compressBy(
    sortBy(data, 'deliveryStatus'),
    'itemIdStr',
    (preGoods, currentGoods) => {
      const { goodsNum, distCompanyName = '', expressNo = '', expressWaybills = [] } = preGoods;
      if (!currentGoods) {
        return {
          ...preGoods,
          expressWaybills: [{ distCompanyName, expressNo }]
        };
      }

      return {
        ...preGoods,
        goodsNum: goodsNum + currentGoods.goodsNum,
        expressWaybills: expressWaybills.concat(
          pick(currentGoods, ['distCompanyName', 'expressNo'])
        )
      };
    }
  );
  const renderExpand = (record) =>
    goodsSpreads[record.itemIdStr] && (isSelfFetch || selectedItems.includes(record.itemIdStr));

  return (
    <Grid
      columns={goodsColumns}
      rowKey="itemIdStr"
      datasets={goodsList}
      expandation={{
        isExpanded(record) {
          return renderExpand(record);
        },
        expandRender(record) {
          return renderExpand(record) ? (
            <RealWeightEntry
              onGoodsSpreadsChange={onGoodsSpreadsChange}
              goods={record}
              spreads={goodsSpreads[record.itemIdStr]}
              indent={!isSelfFetch}
            />
          ) : null;
        }
      }}
    />
  );
}

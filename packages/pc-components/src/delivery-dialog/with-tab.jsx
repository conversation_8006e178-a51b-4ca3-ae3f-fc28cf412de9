import React, { useState, useEffect, useCallback } from 'react';
import { Tabs } from 'zent';
import { get } from 'lodash';

import { ExpressType } from '@youzan/order-domain-definitions';

import { getWaybillVersion } from './api';

const { Express } = ExpressType;

const withTab = (Cpm) => (props) => {
  const { data, tabRef, electronWayBillServiceKdtIdMap, ...others } = props;

  const [activeId, setActiveId] = useState(0);
  const [waybillVersion, setWaybillVersion] = useState(0);
  const [currentElectronWayBillServiceKdtId, setCurrentElectronWayBillServiceKdtId] = useState(0);
  const [currentElectronWayBillServiceKdtName, setCurrentElectronWayBillServiceKdtName] =
    useState('');

  const fetchWaybillVersion = useCallback(async (kdtId) => {
    const res = await getWaybillVersion(kdtId);
    return res;
  }, []);

  const handleGetNewWayBillInfo = useCallback(async () => {
    const activeData = data[activeId];
    const isExpress = activeData?.distInfo?.distType === Express;

    if (isExpress) {
      // 单店的deliveryNo不在data下,所以换个方式获取
      const fulfillNo = get(activeData, ['itemInfos', 0, 'fulfillNo'], 0);
      // 获取当前电子面单服务店铺
      const currentElectronWayBillServiceKdtId = get(
        electronWayBillServiceKdtIdMap,
        ['fulfillExpressWayBillServeSupplyMap', fulfillNo, 'serveSupplyKdtId'],
        0
      );

      // 获取当前电子面单服务店铺名称
      const currentElectronWayBillServiceKdtName = get(
        electronWayBillServiceKdtIdMap,
        ['fulfillExpressWayBillServeSupplyMap', fulfillNo, 'serveSupplyShopName'],
        0
      );

      setCurrentElectronWayBillServiceKdtId(currentElectronWayBillServiceKdtId);
      setCurrentElectronWayBillServiceKdtName(currentElectronWayBillServiceKdtName);
      if (currentElectronWayBillServiceKdtId) {
        const waybillVersion = await fetchWaybillVersion(currentElectronWayBillServiceKdtId);
        setWaybillVersion(waybillVersion);
      }
    }
  }, [data, activeId, electronWayBillServiceKdtIdMap, fetchWaybillVersion]);

  useEffect(() => {
    handleGetNewWayBillInfo();
  }, [handleGetNewWayBillInfo]);

  const tabs = props.data.map((item, i) => ({
    key: i,
    title: `仓${i + 1}`
  }));

  const onChangeTab = (id) => {
    setActiveId(id);
    setCurrentElectronWayBillServiceKdtId(0);
    setWaybillVersion(0);
  };

  return (
    <div>
      <Cpm
        {...others}
        data={data[activeId] || {}}
        ref={tabRef}
        currentTabId={activeId}
        currentElectronWayBillServiceKdtId={currentElectronWayBillServiceKdtId}
        currentElectronWayBillServiceKdtName={currentElectronWayBillServiceKdtName}
        waybillVersion={waybillVersion}
        renderTabComponent={() =>
          data.length > 1 ? (
            <Tabs type="card" activeId={activeId} onChange={onChangeTab} tabs={tabs} />
          ) : null
        }
      />
    </div>
  );
};

export default withTab;

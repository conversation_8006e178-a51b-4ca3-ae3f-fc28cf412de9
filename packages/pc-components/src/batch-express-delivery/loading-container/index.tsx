/**
 * loading容器，可跨组件展示loading效果
 */

import React, { useState, useEffect } from 'react';
import { BlockLoading } from 'zent';
import getEvent from '../event';

const SHOW_LOADING = 'SHOW_LOADING';
const HIDE_LOADING = 'HIDE_LOADING';
interface IProps {
  id: string;
  children?: React.ReactNode;
}

const LoadingContainer = (props: IProps) => {
  const [loading, setLoading] = useState(false);
  const showLoading = () => {
    setLoading(true);
  };
  const hideLoading = () => {
    setLoading(false);
  };
  useEffect(() => {
    getEvent().on(`${props.id}_${SHOW_LOADING}`, showLoading);
    getEvent().on(`${props.id}_${HIDE_LOADING}`, hideLoading);
    return () => {
      getEvent().off(`${props.id}_${SHOW_LOADING}`, showLoading);
      getEvent().off(`${props.id}_${HIDE_LOADING}`, hideLoading);
    };
  }, [props.id]);
  return <BlockLoading loading={loading}>{props.children}</BlockLoading>;
};

export default LoadingContainer;

export const showLoading = (id: string) => {
  getEvent().emit(`${id}_${SHOW_LOADING}`);
};
export const hideLoading = (id: string) => {
  getEvent().emit(`${id}_${HIDE_LOADING}`);
};

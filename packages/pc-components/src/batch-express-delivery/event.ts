/**
 * 事件订阅发布方法
 */

class EventBus {
  bus: HTMLElement;

  constructor() {
    this.bus = document.createElement('loadingEvent');
  }

  on(event, callback) {
    this.bus.addEventListener(event, callback);
  }

  off(event, callback) {
    this.bus.removeEventListener(event, callback);
  }

  emit(event, detail = {}) {
    this.bus.dispatchEvent(new CustomEvent(event, { detail }));
  }
}
let evt: EventBus;
const getEvent = () => {
  if (!evt) {
    evt = new EventBus();
  }
  return evt;
};

export default getEvent;

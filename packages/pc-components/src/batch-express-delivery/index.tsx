import React from 'react';
import { Dialog, Notify } from 'zent';

import BatchDeliveryPrintDialog from './batch-delivery-dialog';
import { IOrderItem } from './batch-print-precheck/type';
import preCheckBatchDeliveryPrintProcess from './batch-print-precheck';

const { openDialog, closeDialog } = Dialog;

const BATCH_DELIVERY_PRINT_DIALOG_ID = 'BATCH_DELIVERY_PRINT_DIALOG_ID';

export function closeBatchDeliveryDialog() {
  closeDialog(BATCH_DELIVERY_PRINT_DIALOG_ID);
}

interface IBatchDeliveryDialogProps {
  orders: IOrderItem[];
  onClose?: () => void;
  onSubmit?: () => void;
}
/**
 * 批量打单发货弹窗
 *
 * @param options
 *  - orders: IOrderItem[] 需要批量打单的订单列表
 *  - onClose?: () => void 弹窗关闭回调函数
 *  - onSubmit?: () => void 提交打单回调函数
 * @param parentComponent 可选，弹窗的父组件
 */
export function openBatchDeliveryDialog(
  options: IBatchDeliveryDialogProps,
  parentComponent = undefined
) {
  // 获取订单列表
  const { orders, onClose, onSubmit } = options;
  // 预检查订单是否可以批量打单
  preCheckBatchDeliveryPrintProcess(orders)
    .then((orderNos: string[]) => {
      const printOrders = orderNos.map((orderNo) =>
        orders.find((item) => item.orderNo === orderNo)
      );
      // 预检查通过，打开弹窗
      openDialog({
        dialogId: BATCH_DELIVERY_PRINT_DIALOG_ID,
        parentComponent,
        className: 'batch-delivery-dialog',
        maskClosable: false,
        style: { width: 800 },
        title: '批量打单发货',
        onClose: () => {
          onClose();
        },
        children: <BatchDeliveryPrintDialog onSubmit={onSubmit} orders={printOrders} />
      });
    })
    .catch((err) => {
      onClose();
      // 预检查失败，显示错误提示
      Notify.error(err);
    });
}

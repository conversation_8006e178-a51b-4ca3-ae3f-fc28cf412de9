import retailAjax from '@youzan/retail-ajax';

// 创建批量打单发货任务
export const createBatchDeliveryPrintTask = (data) => {
  const url = '/v4/trade/delivery/batch-print/create-batch-delivery-print-task.json';
  return retailAjax({
    url,
    method: 'POST',
    data
  });
};

export const createBatchDeliveryPrintTaskReail = (data) => {
  const url = '/youzan.logistics.batchdelivery.apply.deliveyprint/1.0.0';
  return retailAjax({
    url,
    method: 'POST',
    data
  });
};

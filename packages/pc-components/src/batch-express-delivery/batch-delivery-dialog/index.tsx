import React, { useCallback, useEffect, useState } from 'react';
import { Form } from '@zent/compat';
import { Notify } from 'zent';
import { isHqStore, isRetailShop } from '@youzan/utils-shop';
import { get } from 'lodash';

import LoadingContainer, { showLoading, hideLoading } from '../loading-container';
import { openBatchPrintTaskProgressDialog } from '../batch-print-dialog';
import { BatchDeliveryPrinterTaskType } from '../types';
import { createBatchDeliveryPrintTask, createBatchDeliveryPrintTaskReail } from './api';
import { getElectronWayBillServiceKdtId } from '../../delivery-dialog/api';

import { defaultExpressData } from '../../delivery-dialog/default';
import ExpressExtraSystemCall from '../../delivery-dialog/components/express-extra-system-call';
import {
  ExpressWayBillFormType,
  PaymentTypeEnum
} from '../../delivery-dialog/components/express-extra-system-call/types';
import DeliveryBtn from './delivery-btn';
import { IOrderItem } from '../batch-print-precheck/type';
import { formatExpressData } from '../../delivery-dialog/components/express-extra-system-call/extra/get-express-post-data';

const BatchDeliveryPrintDialogLoadingId = 'BatchDeliveryPrintDialogLoadingId';

interface IBatchDeliveryPrintProps {
  orders: IOrderItem[];
  onSubmit?: () => void;
  handleSubmit: any;
  zentForm: any;
}
const BatchDeliveryPrintForm = (props: IBatchDeliveryPrintProps) => {
  const [electronWayBillServiceKdtIdMap, setElectronWayBillServiceKdtIdMap] = useState<any>({});
  const [currentElectronWayBillServiceKdtId, setCurrentElectronWayBillServiceKdtId] =
    useState<number>(0);
  const [currentElectronWayBillServiceKdtName, setCurrentElectronWayBillServiceKdtName] =
    useState<string>('');

  const { zentForm, handleSubmit, orders, onSubmit } = props;
  const orderNos = orders.map((item) => item.orderNo);
  // 微商城仓库id和名称取_global下的值
  const { kdtId, shopName } = _global;

  const [expressWayBill, setExpressWayBill] = useState<any>(defaultExpressData.express);
  const [loading, setLoading] = useState<boolean>(false);
  const [expressHelper, setExpressHelper] = useState<any>(null);

  useEffect(() => {
    // 如果是零售店铺，需要获取电子面单服务商信息
    if (isRetailShop) {
      // 从第一个订单中提取必要的参数
      const { orderNo, deliveryNo, fulfillNo } = orders[0];
      const queryParams = {
        orderNo,
        deliveryNo,
        sourceType: 1 // 来源类型：1表示批量发货
      };

      // 获取电子面单服务商店铺ID映射关系
      getElectronWayBillServiceKdtId(queryParams).then((res) => {
        // 设置电子面单服务商店铺ID映射表
        setElectronWayBillServiceKdtIdMap(res);

        // 从响应中获取当前履约单对应的服务商店铺ID
        const kdtId = get(
          res,
          `fulfillExpressWayBillServeSupplyMap.${fulfillNo}.serveSupplyKdtId`,
          0
        );

        // 从响应中获取当前履约单对应的服务商店铺名称
        const name = get(
          res,
          `fulfillExpressWayBillServeSupplyMap.${fulfillNo}.serveSupplyShopName`,
          ''
        );

        // 设置当前电子面单服务商店铺ID
        setCurrentElectronWayBillServiceKdtId(kdtId);
        // 设置当前电子面单服务商店铺名称
        setCurrentElectronWayBillServiceKdtName(name);
      });
    }
  }, [orders]); // 依赖订单列表变化时重新执行

  const getPostData = () => {
    const {
      auditNo,
      fakeId,
      expressWayBillAddress,
      printerDeviceNo,
      expressId,
      expressName,
      sendType,
      paymentType,
      brandCode,
      productCode,
      eWaybillTemplateId,
      weight,
      startTime,
      endTime,
      logisticsServices
    } = formatExpressData(expressWayBill);

    const postData: any = {
      auditNo,
      fakeId,
      expressWayBillAddress,

      expressId,
      expressName,
      expressWayBillType: sendType,
      paymentType,

      productCode,
      templateUrl: eWaybillTemplateId,
      brandCode,
      printerId: printerDeviceNo,
      waybillVersion: 2,
      logisticsServices
    };

    if (weight) {
      postData.weight = weight;
    }

    if (startTime) {
      postData.startAppointment = startTime;
    }
    if (endTime) {
      postData.endAppointment = endTime;
    }
    return postData;
  };

  const handleWscSubmit = async () => {
    const expressWayBill: any = getPostData();

    const params = {
      orderNos,
      expressWayBill
    };

    return createBatchDeliveryPrintTask(params);
  };

  const handleRetailSubmit = async () => {
    const expressWayBill: any = getPostData();
    const { warehouseId } = orders[0];
    const { userInfo = {} } = _global.business || ({} as any);
    const params: any = {
      orderNos: orders.map((item) => `${item.orderNo},${item.deliveryNo},${item.fulfillNo}`),
      expressWayBill,
      batchBiz: 'BATCH_DELIVERY_PRINT',
      batchSource: 'RETAIL',
      operator: {
        operatorId: userInfo.userId || _global.userId,
        operatorName: userInfo.nickName || _global.operator,
        operatorPhone: userInfo.mobile || _global.mobile
      }
    };

    if (isHqStore) {
      if (warehouseId !== currentElectronWayBillServiceKdtId) {
        expressWayBill.applyKdtId = currentElectronWayBillServiceKdtId;
      }
      params.deliveryPointId = warehouseId;
    }
    return createBatchDeliveryPrintTaskReail(params);
  };

  // 确认打单发货
  const handleConfirm = async () => {
    setLoading(true);
    expressHelper.setExpressStorage();
    try {
      await expressHelper.beforeConfirmShipment();
    } catch (err) {
      setLoading(false);
      return;
    }
    const submitFn = isRetailShop ? handleRetailSubmit : handleWscSubmit;
    submitFn()
      .then((data) => {
        onSubmit?.();
        // // 批量发货完成后展示进度
        openBatchPrintTaskProgressDialog({
          type: BatchDeliveryPrinterTaskType.Create,
          batchNo: data.batchNo,
          printer: (expressWayBill.printerDeviceNo || expressWayBill.printerId) as string,
          expressWayBillType: expressWayBill.expressWayBillType || expressWayBill.sendType
        });
      })
      .catch((err) => {
        Notify.error(err?.msg || err?.message || err || '打单发货失败');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleExpressValueChange = useCallback((val, isCover = false) => {
    if (isCover) {
      setExpressWayBill({
        ...val
      });
    } else {
      setExpressWayBill((prevState) => ({
        ...prevState,
        ...val
      }));
    }
  }, []);
  const handleGenerateHelper = useCallback((helper) => {
    setExpressHelper(helper);
  }, []);

  const disabled =
    !zentForm.isValid() ||
    (!expressWayBill.auditNo && !expressWayBill.fakeId) ||
    (expressWayBill.paymentType === PaymentTypeEnum.Authority &&
      expressWayBill.YZShoppingInfo?.waitJoin &&
      !expressWayBill.agreeProtocol) ||
    (expressWayBill.paymentType === PaymentTypeEnum.Authority &&
      expressWayBill.YZShoppingInfo?.suspended);

  const footerExtra = (expressHelper && expressHelper.renderFooterExtra()) || null;

  return (
    <div className="batch-express-dialog-content">
      <Form horizontal className="delivery-content">
        <ExpressExtraSystemCall
          orderNos={orderNos}
          express={expressWayBill}
          waybillVersion={2}
          formType={ExpressWayBillFormType.BatchDeliveryPrint}
          onExpressValueChange={handleExpressValueChange}
          generateHelper={handleGenerateHelper}
          currentElectronWayBillServiceKdtId={currentElectronWayBillServiceKdtId || kdtId}
          currentElectronWayBillServiceKdtName={currentElectronWayBillServiceKdtName || shopName}
          electronWayBillServiceKdtIdMap={electronWayBillServiceKdtIdMap}
        />
      </Form>
      <div className="action-footer" style={{ textAlign: 'right' }}>
        {footerExtra}
        <DeliveryBtn loading={loading} disabled={disabled} onClick={handleSubmit(handleConfirm)} />
      </div>
    </div>
  );
};

const FormContainer = Form.createForm()(BatchDeliveryPrintForm);

const BatchDeliveryPrintDialog = (
  props: Omit<IBatchDeliveryPrintProps, 'zentForm' | 'handleSubmit'>
) => {
  useEffect(() => {
    showLoading(BatchDeliveryPrintDialogLoadingId);
    setTimeout(() => {
      hideLoading(BatchDeliveryPrintDialogLoadingId);
    }, 800);
  }, []);
  return (
    <LoadingContainer id={BatchDeliveryPrintDialogLoadingId}>
      <div className="od-express-content">
        <FormContainer {...props} />
      </div>
    </LoadingContainer>
  );
};

export default BatchDeliveryPrintDialog;

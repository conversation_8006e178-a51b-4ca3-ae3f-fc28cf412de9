import { get } from 'lodash';
import { IOrderItem } from './batch-print-precheck/type';
import { getElectronWayBillServiceKdtId, getWaybillVersion } from '../delivery-dialog/api';

/**
 * 获取指定订单的电子面单服务提供商的kdtId。
 * 使用提供的订单号、配送号和履约号查询服务。
 *
 * @param {IOrderItem} params - 订单参数。
 * @param {string} params.orderNo - 订单号。
 * @param {string} params.deliveryNo - 配送号。
 * @param {string} params.fulfillNo - 履约号。
 * @returns {Promise<string>} 电子面单服务提供商的kdtId。
 */
export const fetchElectronWayBillServiceKdtId = async function ({
  orderNo,
  deliveryNo,
  fulfillNo
}: IOrderItem) {
  const queryParams = {
    orderNo,
    deliveryNo,
    sourceType: 1
  };
  const applyKdtId = await getElectronWayBillServiceKdtId(queryParams).then((res) => {
    const kdtId = get(res, `fulfillExpressWayBillServeSupplyMap.${fulfillNo}.serveSupplyKdtId`, '');
    return kdtId;
  });
  return applyKdtId;
};

/**
 * 获取订单对应的电子面单版本号
 * 首先获取订单的电子面单服务提供商kdtId，然后查询该服务提供商的面单版本
 *
 * @param {IOrderItem} orderInfo - 订单信息，包含订单号、配送号和履约号
 * @returns {Promise<number>} 返回电子面单版本号
 */
export const getServiceKdtIdWaybillVersion = async function (orderInfo: IOrderItem) {
  const applyKdtId = await fetchElectronWayBillServiceKdtId(orderInfo);
  const waybillVersion = await getWaybillVersion(applyKdtId || orderInfo.warehouseId);
  return waybillVersion;
};

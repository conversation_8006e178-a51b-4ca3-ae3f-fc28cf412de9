/**
 * 批量打单发货任务状态
 * 0-初始值，未处理; 1-处理中; 2-成功; 3-失败; 10-打印中; 20-中断
 */
export enum BatchDeliveryPrintTaskStatus {
  /** 初始值，未处理 */
  Initial = 0,
  /** 处理中 */
  Process = 1,
  /** 成功 */
  Success = 2,
  /** 失败 */
  Fail = 3,
  /** 打印中 */
  Waiting = 10,
  /** 中断 */
  BreakOff = 20,
}

/**
 * 批量发货打印任务明细状态
 * 1-成功; 2-物流发货失败; 3-处理超时，请查看页面数据; 12-打印失败 20-中断 30-处理中
 */
export enum BatchDeliveryPrintTaskItemStatus {
  /** 处理成功(进行中) */
  Success = 1,
  /** 处理失败(物流发货失败) */
  Fail = 2,
  /** 处理超时 */
  Timeout = 3,
  /** 打印失败（打印面单失败） */
  PrintError = 12,
  /** 中断 */
  BreakOff = 20,
  /** 处理中（聚合状态） */
  Process = 30,
}

/**
 * 批量打单发货打印任务类型
 */
export enum BatchDeliveryPrinterTaskType {
  /** 创建批量任务 */
  Create,
  /** 继续批量任务 */
  Continue,
  /** 重新打单 */
  Reprint,
}

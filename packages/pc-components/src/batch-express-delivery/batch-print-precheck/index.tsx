import React from 'react';
import { isHqStore, isRetailShop, isRetailChainStore } from '@youzan/utils-shop';
import { Notify, Sweetalert } from 'zent';
import { preCheckEnable, preCheckEnableRetail } from './api';
import PreCheckNotAllowReason from './content';
import { IOrderItem } from './type';
import { getServiceKdtIdWaybillVersion } from '../utils';
import { WaybillVersionEnum } from '../../delivery-dialog/types';
import { getWaybillVersion } from '../../delivery-dialog/api';

export default async function preCheckBatchDeliveryPrintProcess(orders: IOrderItem[]) {
  const warehouseIds = Array.from(new Set(orders.map((item) => item.warehouseId)));
  if (warehouseIds.length > 1) {
    return Promise.reject('仅可对同一个发货方的订单进行批量操作');
  }

  // 获取当前履约店铺的电子面单版本，零售连锁总部需要单独处理
  const waybillVersion =
    isRetailChainStore && isHqStore
      ? await getServiceKdtIdWaybillVersion(orders[0])
      : await getWaybillVersion(orders[0].warehouseId);
  if (waybillVersion !== WaybillVersionEnum.New) {
    return Promise.reject('该订单履约店铺不支持批量打单发货');
  }

  const orderNos = orders.map((item) => item.orderNo);
  const handleFn = isRetailShop ? preCheckEnableRetail : preCheckEnable;
  return handleFn({ orderNos })
    .then((res) => {
      const { notAllowList = [], allowDeliveryPrintList = [] } = res;

      if (allowDeliveryPrintList.length === 0) {
        return Promise.reject(
          '请选择配送方式为快递发货，订单状态为待发货的订单（货到付款及运费到付暂不支持）'
        );
      }
      if (notAllowList.length === 0) {
        return Promise.resolve(allowDeliveryPrintList);
      }
      return new Promise((resolve) => {
        Sweetalert.confirm({
          title: '提示',
          className: 'unable-batch-delivery-dialog',
          content: <PreCheckNotAllowReason notAllowList={notAllowList} />,
          confirmText: '继续',
          onConfirm() {
            resolve(allowDeliveryPrintList);
          },
          onCancel() {
            resolve(null);
          }
        });
      });
    })
    .catch((err) => {
      Notify.error(err?.msg || err?.message || err);
    });
}

import React from 'react';
import { INotAllowDTO } from './type';

interface IProps {
  notAllowList: INotAllowDTO[];
}
export default function PreCheckNotAllowReason({ notAllowList }: IProps) {
  return (
    <div>
      <div>所选订单中有{notAllowList.length}个不可操作批量打单发货，是否过滤后继续操作?</div>
      <div className="unable-batch-delivery-list">
        {notAllowList.map(item => {
          return (
            <div className="unable-batch-delivery-item" key={item.orderNo}>
              {item.orderNo}&nbsp;&nbsp;{item.notAllowReason}
            </div>
          );
        })}
      </div>
    </div>
  );
}

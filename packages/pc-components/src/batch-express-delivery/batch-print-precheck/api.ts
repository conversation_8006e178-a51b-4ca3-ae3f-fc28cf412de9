import retailAjax from '@youzan/retail-ajax';
import { IPreCheckEnableResponse } from './type';

// 检查订单是否可批量打单发货
export const preCheckEnable = (data: { orderNos: string[] }): Promise<IPreCheckEnableResponse> => {
  return retailAjax({
    url: '/v4/trade/delivery/batch-print/pre-check.json',
    method: 'POST',
    data
  });
};

export const preCheckEnableRetail = (data: {
  orderNos: string[];
}): Promise<IPreCheckEnableResponse> => {
  return retailAjax({
    url: '/youzan.logistics.batchdelivery.verify/1.0.0',
    method: 'POST',
    data
  });
};

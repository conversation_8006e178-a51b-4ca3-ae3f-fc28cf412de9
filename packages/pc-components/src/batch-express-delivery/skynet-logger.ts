import Zan<PERSON>ebLogger from '@youzan/zan-web-logger';

ZanWebLogger.config({
  appName: 'wsc-pc-trade'
});

const reportSkynet = (level, options) => {
  try {
    ZanWebLogger.log({
      name: options.name,
      message: options.detail ? JSON.stringify(options.detail) : '',
      level,
      extra: {
        kdtId: _global.kdtId,
        userId: _global.business?.userInfo?.id || _global.userId,
        path: window.location.pathname,
        ...options.extra
      }
    });
  } catch (err) {
    console.error(err);
  }
};

const skynet = {
  info(name, detail?: any, extra?: any) {
    reportSkynet('info', {
      name,
      detail,
      extra
    });
  },
  warn(name, detail?: any, extra?: any) {
    reportSkynet('warn', {
      name,
      detail,
      extra
    });
  },
  error(name, detail?: any, extra?: any) {
    reportSkynet('error', {
      name,
      detail,
      extra
    });
  }
};

export default skynet;

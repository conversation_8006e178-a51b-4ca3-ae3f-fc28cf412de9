import React from 'react';
import { Dialog } from 'zent';

import BatchPrintTaskProgress, { IBatchPrintTaskProgressProps } from './content';

import getEvent from '../event';

const { openDialog, closeDialog } = Dialog;
const dialogId = 'batch-print-dialog-id';

// 批量打单发货进度弹窗关闭事件
export const BatchPrintTaskProgressDialogCloseEvent = 'BatchPrintTaskProgressDialogCloseEvent';

export const closeBatchPrintTaskProgressDialog = () => {
  closeDialog(dialogId);
};

interface IBatchPrintTaskProgressDialogProps extends IBatchPrintTaskProgressProps {
  title?: string;
  onClose?: () => void;
}
export const openBatchPrintTaskProgressDialog = (
  options: IBatchPrintTaskProgressDialogProps,
  parentComponent = undefined
) => {
  const { title = '打单发货进度', onClose } = options;
  openDialog({
    dialogId,
    parentComponent,
    className: 'batch-print-dialog-wrap',
    style: {
      width: 420
    },
    maskClosable: false,
    title,
    onClose: () => {
      getEvent().emit(BatchPrintTaskProgressDialogCloseEvent);
      closeBatchPrintTaskProgressDialog();
      onClose?.();
    },
    children: <BatchPrintTaskProgress {...options} />
  });
};

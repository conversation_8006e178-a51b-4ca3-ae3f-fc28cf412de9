import { Notify } from 'zent';
import { isRetailShop } from '@youzan/utils-shop';
import { doPrint } from '../../cainiao-printer/printer';
import {
  getBatchDeliveryPrintData,
  updateBatchItemPrintState,
  updateBatchItemPrintStateRetail,
  queryProgress
} from './api';
import getEvent from '../event';
import { BatchDeliveryPrintTaskStatus, BatchDeliveryPrinterTaskType } from '../types';
import { IUpdateBatchItemPrintState } from './types';
import skynet from '../skynet-logger';

import { EXPRESS_WAY_BILL_TYPES } from '../../delivery-dialog/components/express-extra-system-call/common/constants';

export const BatchPrintTaskCreateEvent = 'BatchPrintTaskCreateEvent'; // 批量任务开始事件key
export const BatchPrintTaskNotifyEvent = 'BatchPrintTaskNotifyEvent'; // 批量任务通知事件key
export const BatchPrintTaskEndEvent = 'BatchPrintTaskEndEvent'; // 批量任务结束事件key

// 批量任务失败 code
export enum BatchTaskErrorCodeMap {
  Fail = 101,
  Timeout = 102
}

// 打印超时时间
const printTimeout = 10000;
// 循环获取批量任务状态每一步的延迟时间
const loopGetBatchStatusDelayTime = 2000;

// 延迟生成器
export function timeoutMaker(time = 1500) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(void 0);
    }, time);
  });
}

/**
 * 递归获取批量任务的状态，直到获取到成功为止
 * 1. 每次获取请求间隔延迟n秒
 * 2. 最大尝试次数 maxCount 次
 */
export async function loopGetBatchTaskStatus(batchNo: string) {
  const maxCount = 5; // 最大尝试次数

  for (let i = 0; i < maxCount; i++) {
    try {
      // eslint-disable-next-line no-await-in-loop
      const result = await queryProgress(batchNo);
      // 可批量打印状态：等待打印中、中断 & 成功/失败也展示打印进度，100%，失败数量为总和
      if (
        [
          BatchDeliveryPrintTaskStatus.Success,
          BatchDeliveryPrintTaskStatus.Fail,
          BatchDeliveryPrintTaskStatus.Waiting,
          BatchDeliveryPrintTaskStatus.BreakOff
        ].includes(result.batchStatus)
      ) {
        return Promise.resolve(true);
      }
    } catch (err) {
      Notify.error(err?.msg || err || '网络异常，请稍后重试');
      // 获取进度接口报错，直接返回任务失败
      console.error('loopGetBatchTaskStatus.err', err);
      return Promise.reject({ msg: '批量任务处理失败', code: BatchTaskErrorCodeMap.Fail });
    }

    // eslint-disable-next-line no-await-in-loop
    await timeoutMaker(loopGetBatchStatusDelayTime);
  }
  skynet.info('批量任务获取超时', { batchNo }, { batchNo });
  return Promise.reject({ msg: '批量任务处理中', code: BatchTaskErrorCodeMap.Timeout });
}

interface IItemPrintOptions
  extends Pick<
    IBatchPrintTaskOptions,
    'batchNo' | 'printer' | 'templateUrl' | 'expressWayBillType'
  > {
  type: BatchDeliveryPrinterTaskType;
  batchItemNo: number;
}
// 单个明细打印任务并更新明细状态
export async function handleBatchItemPrint(
  opt: IItemPrintOptions
): Promise<{ success: boolean; message: string }> {
  const { type, batchNo, batchItemNo, printer, templateUrl, expressWayBillType } = opt;
  // 打印任务流程
  const printTask =
    // 发货类型为“快递员上门打印”时不真的打印，统统算作打印成功
    expressWayBillType === EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value
      ? Promise.resolve()
      : getBatchDeliveryPrintData({
          batchNo,
          batchItemNo,
          templateUrl
        }).then((printData) => doPrint(printer, printData));
  // 延迟器，n秒打印流程不成功则视为超时，上报天网并终止流程
  const timeoutTask = timeoutMaker(printTimeout).then(() => {
    return Promise.reject({ msg: '打印超时', code: BatchTaskErrorCodeMap.Timeout });
  });
  return Promise.race([printTask, timeoutTask])
    .then(async () => {
      const postData = {
        batchNo,
        batchItemNo,
        printSuccess: true,
        /**
         * 【Q】resetInterruptTask什么用途？
         * 【A】批量任务是后端异步执行的，所有任务明细都是默认都是等待打印中，但是由于前端依次打印过程中，商家可能会关闭浏览器，
         * 此时就出现了后端收不到任何是否打印成功的消息，批次任务状态一直处于打印中，因此我们需要将这种情况，将任务打标为任务中断。
         * 如何打标？后端在创建批量任务时，创建一个超时任务，如果 20s 没有收到前端的任务明细状态更新请求（也就是当前这个请求），则视为批次任务中断从而打标
         * 【Q】为什么需要传resetInterruptTask？
         * 【A】因为批量打印任务，有创建/继续/重打多重流程，只有创建流程，后端才会执行中断能力，因此需要前端区分是否需要不同流程给到后端
         */
        resetInterruptTask: type === BatchDeliveryPrinterTaskType.Create
      };
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      await afterPrint(postData);
      return {
        success: true,
        message: ''
      };
    })
    .catch(async (err) => {
      const failMsg = err?.msg || err?.message || err;
      const postData = {
        batchNo,
        batchItemNo,
        printSuccess: false,
        failMsg,
        resetInterruptTask: type === BatchDeliveryPrinterTaskType.Create
      };
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      await afterPrint(postData);
      // 超时上报个天网，看一下量级
      if (err?.code === BatchTaskErrorCodeMap.Timeout) {
        skynet.info('批量打单发货打印超时', { batchNo, batchItemNo }, { batchNo, batchItemNo });
      }
      return {
        success: false,
        message: failMsg
      };
    });
}

async function afterPrint(postData: IUpdateBatchItemPrintState) {
  getEvent().emit(BatchPrintTaskNotifyEvent, postData);

  const maxCount = 3; // 更新失败的重试三次，三次都失败继续往下打印
  let retryCount = 1;
  const updateFn = isRetailShop ? updateBatchItemPrintStateRetail : updateBatchItemPrintState;
  // 更新单条打印状态
  const doUpdate = async () => {
    await updateFn(postData).catch(async () => {
      if (retryCount < maxCount) {
        retryCount++;
        await doUpdate();
      }
    });
  };
  await doUpdate();
}

interface IBatchPrintTaskOptions {
  type: BatchDeliveryPrinterTaskType; // 来源
  batchNo: string; // 批量任务编码
  batchItemNoList: number[]; // 批量任务明细编号
  printer: string; // 选择的打印机
  templateUrl?: string; // 选择的模板
  expressWayBillType?: number; // 发货类型
}

// 创建一个批量打印任务
export default async function handleCreateBatchPrintTask(options: IBatchPrintTaskOptions) {
  const { type, batchNo, batchItemNoList, printer, templateUrl, expressWayBillType } = options;
  for (let i = 0; i < batchItemNoList.length; i++) {
    const batchItemNo = batchItemNoList[i];
    // eslint-disable-next-line no-await-in-loop
    await handleBatchItemPrint({
      type,
      batchNo,
      batchItemNo,
      printer,
      templateUrl,
      expressWayBillType
    });
  }
  getEvent().emit(BatchPrintTaskEndEvent, { batchNo });
}

import retailAjax from '@youzan/retail-ajax';
import { IUpdateBatchItemPrintState, IGetBatchDeliveryPrintDataParams } from './types';

// 获取批量任务打印数据
export const getBatchDeliveryPrintData = (data: IGetBatchDeliveryPrintDataParams) => {
  return retailAjax({
    url: '/v4/trade/delivery/batch-print/print-data.json',
    method: 'GET',
    data
  });
};

// 更新批量任务明细的状态
export const updateBatchItemPrintState = (data: IUpdateBatchItemPrintState) => {
  return retailAjax({
    url: '/v4/trade/delivery/batch-print/update-batch-print-status.json',
    method: 'POST',
    data
  });
};
// 更新批量任务明细的状态
export const updateBatchItemPrintStateRetail = (data: IUpdateBatchItemPrintState) => {
  return retailAjax({
    url: '/youzan.logistics.batchdelivery.update.printstatus/1.0.0',
    method: 'POST',
    data
  });
};

// 获取任务进度
export const queryProgress = (batchNo: string) => {
  return retailAjax({
    url: '/v4/trade/delivery/batch/progress.json',
    method: 'GET',
    data: {
      batchNo
    }
  });
};

// 获取批量选择发货记录
export const getBatchDeliveryDetail = (data) => {
  return retailAjax({
    url: '/v4/trade/delivery/batch/detail.json',
    method: 'GET',
    data
  });
};

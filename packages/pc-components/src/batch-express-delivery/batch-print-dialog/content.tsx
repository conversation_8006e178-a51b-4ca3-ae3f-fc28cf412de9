import React, { useState, useEffect } from 'react';
import { Progress, InlineLoading, Button } from 'zent';
import handleCreateBatchPrintTask, {
  BatchPrintTaskNotifyEvent,
  BatchPrintTaskEndEvent,
  timeoutMaker
} from './batch-print-task';
import { getBatchDeliveryDetail } from './api';
import { BatchDeliveryPrinterTaskType, BatchDeliveryPrintTaskStatus } from '../types';
import getEvent from '../event';

export interface IBatchPrintTaskProgressProps {
  type: BatchDeliveryPrinterTaskType; // 任务类型
  batchNo: string; // 批量任务编码
  printer: string; // 打印机 id
  templateUrl?: string; // 选择的模板
  supportBatchItemNoList?: string[]; // 外部传入需要打印的明细编码列表
  expressWayBillType?: number; // 发货类型
}

const BatchPrintTaskProgress = (props: IBatchPrintTaskProgressProps) => {
  const {
    type,
    batchNo,
    printer,
    templateUrl,
    supportBatchItemNoList = [],
    expressWayBillType
  } = props;

  // 如果外部传入supportBatchItemNoList，是重新打印的情况，不需要获取明细，默认使用supportBatchItemNoList作为打印任务明细
  const [isReady, setReadyState] = useState<boolean>(!!supportBatchItemNoList?.length);
  const [totalCount, setTotalCount] = useState<number>(supportBatchItemNoList?.length || 0);
  const [successCount, setSuccessCount] = useState<number>(0);
  const [failCount, setFailCount] = useState<number>(0);
  const [isReadyFail, setIsReadyFail] = useState<boolean>(false); // 准备任务失败
  const [retryCount, setRetryCount] = useState<number>(0);
  const [initialCount, setInitialCount] = useState<number>(0);
  const [startPrintTime, setStartPrintTime] = useState<number>(0);
  const percent = Math.round(((successCount + failCount) / totalCount) * 100);
  /** 循环获取批次详情列表信息每一步的延迟时间 */
  const loopGetBatchDeliveryDetailDelayTime = 2000;
  /** 循环获取批次详情列表信息，状态为“未处理”的最大尝试次数 */
  const loopGetBatchDeliveryDetailInitialMaxCount = 5;

  const emitBatchPrintTask = (batchItemNoList) => {
    // 触发批量打印任务
    handleCreateBatchPrintTask({
      type,
      batchNo,
      batchItemNoList,
      printer,
      templateUrl,
      expressWayBillType
    });
  };

  // 循环获取批次详情列表信息并打印
  const loopPrint = async (delayTime = 0) => {
    delayTime && (await timeoutMaker(delayTime));

    getBatchDeliveryDetail({
      batchNo,
      pageNum: 1,
      pageSize: 100,
      queryPrintItem: true
    })
      .then((data) => {
        const { batchStatus, list = [], totalItems, successAmount, failAmount } = data;

        if (batchStatus === BatchDeliveryPrintTaskStatus.Initial) {
          // 批次状态为“未处理”，还没查到上限次数则继续查，达到上限次数则显示超时重试
          if (initialCount < loopGetBatchDeliveryDetailInitialMaxCount) {
            setInitialCount(initialCount + 1);
            loopPrint(loopGetBatchDeliveryDetailDelayTime);
          } else {
            setIsReadyFail(true);
          }
        } else {
          // 展示进度条
          setTotalCount(totalItems);
          setSuccessCount(successAmount);
          setFailCount(failAmount);
          setReadyState(true);

          // 批次处理成功或处理失败则表示不用打印了
          if (
            ![BatchDeliveryPrintTaskStatus.Success, BatchDeliveryPrintTaskStatus.Fail].includes(
              batchStatus
            )
          ) {
            const batchItemNoList = list.map((item) => item.batchItemNo);
            if (batchItemNoList.length === 0) {
              loopPrint(loopGetBatchDeliveryDetailDelayTime);
            } else {
              setStartPrintTime(new Date().getTime());
              emitBatchPrintTask(batchItemNoList);
            }
          }
        }
      })
      .catch(() => {
        // 获取明细报错则展示失败界面
        setIsReadyFail(true);
        setRetryCount(4);
      });
  };

  // 开始流程
  const startProcess = () => {
    setIsReadyFail(false);
    loopPrint(loopGetBatchDeliveryDetailDelayTime);
  };

  // 重试流程
  const retryProcess = () => {
    setRetryCount(retryCount + 1);
    startProcess();
  };

  useEffect(() => {
    // 外部传入需要打印的 明细id 列表，则直接执行打印
    if (supportBatchItemNoList.length > 0) {
      emitBatchPrintTask(supportBatchItemNoList);
      return;
    }
    startProcess();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleBatchTaskProgress = ({ detail }) => {
    if (batchNo !== detail.batchNo) {
      return;
    }
    if (detail.printSuccess) {
      setSuccessCount(successCount + 1);
    } else {
      setFailCount(failCount + 1);
    }
  };

  const handleBatchTaskEnd = ({ detail }) => {
    if (batchNo !== detail.batchNo) {
      return;
    }
    const currentTime = new Date().getTime();
    const timeGap = currentTime - startPrintTime;
    if (timeGap > loopGetBatchDeliveryDetailDelayTime) {
      loopPrint();
    } else {
      loopPrint(loopGetBatchDeliveryDetailDelayTime - timeGap);
    }
  };

  useEffect(() => {
    getEvent().on(BatchPrintTaskNotifyEvent, handleBatchTaskProgress);
    return () => {
      getEvent().off(BatchPrintTaskNotifyEvent, handleBatchTaskProgress);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [failCount, successCount, batchNo]);

  useEffect(() => {
    getEvent().on(BatchPrintTaskEndEvent, handleBatchTaskEnd);
    return () => {
      getEvent().off(BatchPrintTaskEndEvent, handleBatchTaskEnd);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [failCount, successCount, batchNo]);

  // 获取任务状态超时
  if (isReadyFail) {
    if (retryCount >= 3) {
      return (
        <div>
          任务处理超时，请到订单-批量发货页面{' '}
          <a
            target="_blank"
            href={`/v4/trade/delivery/batch-detail/batch-delivery-print?batchNo=${batchNo}`}
            rel="noreferrer"
          >
            查看操作结果
          </a>
          或联系客服反馈
        </div>
      );
    }
    return (
      <div className="batch-print-task-progress__ready-fail">
        <div className="batch-print-task-progress__ready-fail-tip">任务处理超时，请点击重试</div>
        <div className="batch-print-task-progress__retry-btn">
          <Button onClick={retryProcess} type="primary">
            重试
          </Button>
        </div>
      </div>
    );
  }
  // 数据未准备好
  if (!isReady) {
    return (
      <div className="batch-print-task-progress-loading">
        <InlineLoading loading />
        <div className="batch-print-task-progress-loading-tip">
          批量操作任务处理中，请勿关闭当前弹窗
        </div>
      </div>
    );
  }

  return (
    <div className="batch-print-task-progress">
      <div className="progress-bar-wrap">
        <Progress percent={percent} status="normal" />
        <div className="batch-print-task-progress-text">
          <span>
            成功<strong>{successCount}</strong>
          </span>
          /
          <span className="batch-print-task-progress-failtext">
            失败<strong>{failCount}</strong>
          </span>
        </div>
      </div>
      {supportBatchItemNoList.length > 0 ? (
        <div>
          进度未到<strong>100</strong>%时请勿关闭当前弹窗，关闭将导致打印中断
        </div>
      ) : (
        <div>
          {percent < 100 && (
            <span>
              进度未到<strong>100</strong>
              %时请勿关闭当前弹窗，关闭将导致批量任务中断；
            </span>
          )}
          关闭弹窗后，可到订单-批量发货页面{' '}
          <a
            target="_blank"
            href={`/v4/trade/delivery/batch-detail/batch-delivery-print?batchNo=${batchNo}`}
            rel="noreferrer"
          >
            查看操作结果
          </a>
        </div>
      )}
    </div>
  );
};

export default BatchPrintTaskProgress;

import React from 'react';
import { Button, openDialog, closeDialog } from 'zent';
import { global, track } from '@youzan/retail-utils';

import { getApolloConfig, checkApolloConfig, checkIsYZShoppingBlackList } from './api';
import { findShipButton } from './utils';

const { KDT_ID, USER_INFO } = global;
const key = `${KDT_ID}_${USER_INFO.id}_risk_warning_dialog_last_shown`;
const RISK_WARNING_DIALOG = 'RISK_WARNING_DIALOG';

interface IProps {
  scene?: 'copy' | 'export';
  orderInfo: any;
  onContinue?: (isCopy?: boolean) => void;
  canCloseDialog?: boolean;
}

/**
 * 风险提示弹窗组件
 *
 * 使用场景：
 * 1. 当用户复制或下载包含敏感信息（如地址、联系方式等）的内容时
 * 2. 当用户进行可能涉及隐私的操作时x
 *
 * 参数说明：
 * @param {Object} orderInfo - 订单信息对象
 * @param {string} scene - 使用场景，可选值：
 *   - 'copy': 复制场景
 *   - 'export': 导出场景
 * @param {Function} onContinue - 用户点击"继续操作"后的回调函数
 * @param {boolean} canCloseDialog - 是否直接关闭风险提示弹窗
 * 使用示例：
 * onShowRiskWarningDialog({
 *   orderInfo: currentOrder,
 *   scene: 'copy',
 *   onContinue: () => {
 *     // 用户确认后继续操作
 *   },
 * });
 */
const onShowRiskWarningDialog = async ({
  scene = 'copy',
  orderInfo,
  onContinue,
  canCloseDialog = true
}: IProps) => {
  const lastShown = localStorage.getItem(key);
  const now = new Date().getTime();
  let limitTime = 7 * 24 * 60 * 60 * 1000;
  let isInWhiteList = true;
  let isInYZShoppingBlackList = true;
  try {
    const [riskWarningTime, isCheckApolloConfig, isCheckYZShoppingBlackList] = await Promise.all([
      // 获取风险提示弹窗周期时间
      getApolloConfig({
        key: 'risk_warning_dialog_time',
        namespace: 'application'
      }),
      // 检查是否在免打扰白名单中
      checkApolloConfig({
        key: 'YZ_shopping_no_request'
      }),
      // 检查是否在有赞寄件黑名单中
      checkIsYZShoppingBlackList()
    ]);
    limitTime = +riskWarningTime;
    isInWhiteList = isCheckApolloConfig;
    isInYZShoppingBlackList = isCheckYZShoppingBlackList;
  } catch (error) {
    console.error(error);
  }

  // 复制场景
  const isCopy = scene === 'copy';
  // 导出场景
  const isExport = scene === 'export';

  const { expressType, state, orderState } = orderInfo || {};
  // 在当前页找到有发货的按钮
  const hasSendButton = findShipButton();
  // 判断是否跳过风险提示弹窗
  // 复制场景
  // 满足以下任一条件时跳过：
  // 1. 非快递发货
  // 2. 非待发货状态
  // 3. 有赞寄件黑名单内
  // 4. 没有发货按钮
  // 5. 在免打扰白名单中
  // 6. 显示过风险提示弹窗，且距离上次显示不超过配置的时间
  const isCopySkip =
    isCopy &&
    (expressType !== 0 ||
      state !== 5 ||
      isInYZShoppingBlackList ||
      (lastShown && now - +lastShown < limitTime) ||
      !hasSendButton ||
      isInWhiteList);

  // 导出场景
  // 满足以下任一条件时跳过：
  // 1. 订单状态不是待发货
  // 2. 有赞寄件黑名单内
  // 3. 显示过风险提示弹窗，且距离上次显示不超过配置的时间
  // 4. 在免打扰白名单中
  const isExportSkip =
    isExport &&
    (orderState !== 'tosend' ||
      isInYZShoppingBlackList ||
      (lastShown && now - +lastShown < limitTime) ||
      isInWhiteList);

  const handleClose = () => {
    closeDialog(RISK_WARNING_DIALOG);
    localStorage.setItem(key, new Date().getTime().toString());
  };

  // 点击立即体验
  const handleExperience = () => {
    handleClose();
    // 如果是复制的场景，那就展示发货弹窗
    if (isCopy) {
      // 触发hasSendButton的click事件
      hasSendButton?.click();
      onContinue && onContinue(false);
      track({
        et: 'click', // 事件类型
        ei: 'to_use_click', // 事件标识
        en: '立即体验点击', // 事件名称
        params: {
          scene,
          component: 'risk_warning_dialog'
        } // 事件参数
      });
    }
    if (isExport) {
      // 新开页面跳转到批量发货使用教程
      window.open('https://help.youzan.com/displaylist/detail_4_4-2-87294', '_blank');
    }
  };

  // 点击继续操作或者跳过风险提示弹窗
  const handleContinue = () => {
    onContinue && onContinue();
  };

  // TODO: 批量发货功能暂未上线，导出场景临时跳过风险提示
  // 待批量发货功能上线后，需要移除此判断逻辑
  const isSkip = isExport;

  // 如果跳过风险提示弹窗，则直接继续操作
  if (isCopySkip || isExportSkip || isSkip) {
    handleContinue();
  } else {
    track({
      et: 'view', // 事件类型
      ei: 'risk_warning_dialog_show', // 事件标识
      en: '有赞寄件风险提示弹窗曝光', // 事件名称
      params: {
        scene,
        component: 'risk_warning_dialog'
      } // 事件参数
    });
    openDialog({
      dialogId: RISK_WARNING_DIALOG,
      title: '风险提示',
      children: (
        <p style={{ width: 600 }}>
          该操作存在信息泄露风险，如{isCopy ? '复制' : '导出'}
          数据用于物流发货，建议使用有赞寄件，{isCopy ? '后台一键取号发货' : '一键批量取号发货'}
          ，安全便捷，更有专属运费折扣，运费低至2.5元。
        </p>
      ),
      footer: (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            width: isCopy ? 320 : 350,
            float: 'right'
          }}
        >
          <Button
            onClick={() => {
              handleClose();
              window.open(
                `/v4/assets/shipping?forceToManual=1&source=risk-warning-dialog-${scene}`,
                '_blank'
              );
            }}
          >
            了解有赞寄件
          </Button>
          <Button onClick={handleExperience}>{isExport ? '如何批量发货' : '立即体验'}</Button>
          <Button
            type="primary"
            onClick={() => {
              track({
                et: 'click', // 事件类型
                ei: 'to_continue_click', // 事件标识
                en: '继续操作点击', // 事件名称
                params: {
                  scene,
                  component: 'risk_warning_dialog'
                } // 事件参数
              });
              handleClose();
              handleContinue();
            }}
          >
            继续操作
          </Button>
        </div>
      ),
      closeBtn: canCloseDialog,
      maskClosable: canCloseDialog,
      onClose: () => handleClose()
    });
  }
};

export { onShowRiskWarningDialog };

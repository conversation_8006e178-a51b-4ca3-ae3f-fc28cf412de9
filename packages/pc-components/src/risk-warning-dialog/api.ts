
import ajax from 'zan-pc-ajax';

export function getDefaultAddress() {
  return ajax({
    url: '/v4/trade/shopAddress/defaultAddress.json',
    method: 'GET',
  });
}

export function getApolloConfig(data: { key: string, namespace: string }) {
  return ajax({
    url: '/v4/trade/common/apolloConfig.json',
    method: 'GET',
    data,
  });
}

export function checkApolloConfig(data: { key: string }) {
  return ajax({
    url: '/v4/trade/common/checkApolloConfig.json',
    method: 'GET',
    data,
  });
}

export function checkIsYZShoppingBlackList() {
  return ajax({
    url: '/v4/trade/api/express/checkIsYZShoppingBlackList.json',
    method: 'GET',
  });
}

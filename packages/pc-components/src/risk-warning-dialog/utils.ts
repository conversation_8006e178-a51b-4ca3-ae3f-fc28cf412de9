/**
 * 查找发货按钮
 * @returns {HTMLElement | null} 返回找到的发货按钮元素，如果未找到则返回null
 */
export const findShipButton = (): HTMLElement | null => {
  try {
    // 1. 直接查找包含"发货"文字的按钮元素，且未被禁用
    const buttons = Array.from(document.querySelectorAll('button'));
    const shipButton = buttons.find(
      (btn) => btn.textContent === '发货' && !btn.classList.contains('zent-btn-disabled')
    );
    if (shipButton) return shipButton as HTMLElement;

    // 2. 如果没找到，扩大搜索范围到任何可点击元素，且未被禁用
    const clickableElements = Array.from(
      document.querySelectorAll('button, [role="button"], .btn, a')
    );
    const shipElement = clickableElements.find(
      (el) => el.textContent === '发货' && !el.classList.contains('zent-btn-disabled')
    );
    if (shipElement) return shipElement as HTMLElement;

    // 3. 最后尝试查找特定的类名（基于代码中的提示），且未被禁用
    const expressSendBtn = document.querySelector('.express-send-btn');
    if (expressSendBtn && !expressSendBtn.classList.contains('zent-btn-disabled')) {
      return expressSendBtn as HTMLElement;
    }
    return null;
  } catch (err) {
    return null;
  }
};

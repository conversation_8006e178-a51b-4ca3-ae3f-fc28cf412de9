/* eslint-disable no-console */
/* eslint-disable no-bitwise */

/**
 * 获取请求的UUID，指定长度和进制,如
 * getUUID(8, 2)   //"01001010" 8 character (base=2)
 * getUUID(8, 10) // "47473046" 8 character ID (base=10)
 * getUUID(8, 16) // "098F4D35"。 8 character ID (base=16)
 *
 */
export function getUUID(len, defaultRadix) {
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  const uuid: string[] = [];
  const radix = defaultRadix || chars.length;
  let i;
  if (len) {
    for (i = 0; i < len; i++) {
      uuid[i] = chars[0 | (Math.random() * radix)];
    }
  } else {
    let r;
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (Math.random() * 16);
        uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
      }
    }
  }
  return uuid.join('');
}
/**
 * 构造request对象
 */
interface IReqeustDTO {
  requestID: string;
  version: string;
  cmd: string;
}
function getRequestObject(cmd) {
  const request: IReqeustDTO = {} as IReqeustDTO;
  request.requestID = getUUID(8, 16);
  request.version = '1.0';
  request.cmd = cmd;
  return request;
}

/**
 * socket连接器
 * const socket = new WebsocketConnect('ws://localhost:8081);
 * socket.ready(() => {
 *  const io = socket.emit('getData');
 *  socket.listen(io, (data) => {
 *    console.log('获取到的data', data)
 *  })
 * })
 */

class WebsocketConnect {
  protected socket;

  private readyCallbacks;

  public isOpen;

  public isClosed;

  private events;

  constructor(url: string, type?: any) {
    const socket = new WebSocket(url, type);
    console.log(`WebSocket start connect by：${url}`);
    this.socket = socket;
    this.isOpen = false;
    this.isClosed = false;
    this.readyCallbacks = [];
    this.events = new Map();
    socket.onopen = this.onopen.bind(this);
    socket.onmessage = this.onmessage.bind(this);
    socket.onclose = this.onclose.bind(this);
  }

  // 链接开启
  private onopen(event) {
    console.log(`WebSocket is opened by：${event.target.url}`);
    this.isOpen = true;
    if (this.readyCallbacks.length > 0) {
      this.readyCallbacks.forEach((cb) => {
        cb(this);
      });
    }
  }

  private onmessage(event) {
    const { showSocketMessage } = _global;
    showSocketMessage && console.log('websocket onmessage: ', event, Date.now());
    const { data } = event;
    try {
      const response = JSON.parse(data);
      const { requestID, ...res } = response;
      showSocketMessage &&
        console.log('websocket onmessage data: ', JSON.parse(JSON.stringify(res)));
      const handler = this.events.get(requestID);
      handler && handler(res);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log('err', err);
    }
  }

  private onclose(event) {
    console.log(`WebSocket is closed by：${event.target.url}`);
    this.isClosed = true;
    this.isOpen = false;
    this.emitReady();
  }

  private send(data) {
    console.log('websocket send: ', data, Date.now());
    this.socket.send(JSON.stringify(data));
  }

  private emitReady() {
    if (this.readyCallbacks.length > 0) {
      this.readyCallbacks.forEach((cb) => {
        cb(this);
      });
    }
  }

  protected ready(callback) {
    if (!this.isOpen) {
      this.readyCallbacks.push(callback);
    } else {
      callback(this);
    }
  }

  /**
   * 执行一个命令，需要配合listen使用
   * @param cmd 要执行的命令
   * @param postData 需要传输的数据
   * @returns {requestId} 请求唯一id
   */
  protected emit(cmd: string, postData?: any) {
    const requestVo = getRequestObject(cmd);
    if (postData) {
      Object.assign(requestVo, postData);
    }
    this.send(requestVo);
    return requestVo.requestID;
  }

  /**
   * 监听某个命令并处理结果
   * @param requestId 请求唯一id
   * @param callback 请求结果的回调函数
   */
  protected listen(requestId: any, callback: (arg0: any) => void) {
    let isSuccess = false;
    this.events.set(requestId, (data) => {
      isSuccess = true;
      callback(data);
    });
    setTimeout(() => {
      if (!isSuccess) {
        // eslint-disable-next-line no-console
        console.error(`${requestId}请求超时`);
        callback(null);
      }
    }, 10000); // 10s还没返回数据，则定义为超时
  }

  /**
   * 执行一个任务，通过promise获取其结果
   * @param cmd 要执行的命令
   * @param postData 需要传输的数据
   * @param callback 回调
   * @returns {requestId} 请求唯一id
   */
  protected task({ cmd, postData = {}, callback }) {
    return new Promise((resolve, reject) => {
      // 如果isClosed，则链接被关闭，reject掉本次请求
      if (this.isClosed) {
        return reject(`${cmd}执行失败: websocket未链接`);
      }
      this.ready((socket) => {
        // 如果isClosed，则链接被关闭，reject掉本次请求，close也会执行ready的回调因此这里也要判断
        if (this.isClosed) {
          return reject();
        }
        const io = socket.emit(cmd, postData);
        socket.listen(io, (data) => {
          // 如果传了callback，执行callback
          callback && callback(data);
          if (!data) {
            return reject();
          }
          resolve(data);
        });
      });
    });
  }
}

export default WebsocketConnect;

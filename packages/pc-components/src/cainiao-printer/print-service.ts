/**
 * 菜鸟打印机交互服务
 * doc: https://support-cnkuaidi.taobao.com/docs/doc.htm?spm=a21da.7386797.0.0.4479604byJJ0Hw&source=search&treeId=409&articleId=107014&docType=1#s3
 */

import skynet from '../batch-express-delivery/skynet-logger';
import WebsocketConnect, { getUUID } from './websocket';

/** 数据格式定义  */
interface IGetPrintersResponseDTO {
  defaultPrinter: string; // 默认打印机名称
  printers: Array<{
    name: string;
    status: string;
  }>;
}

class PrintService {
  public socket;

  constructor(url = 'ws://localhost:13528', type = undefined /* 'echo-protocol' */) {
    this.socket = new WebsocketConnect(url, type);
  }

  // 获取打印机
  getPrinters(): Promise<IGetPrintersResponseDTO> {
    return this.socket.task({
      cmd: 'getPrinters'
    });
  }

  // 执行打印
  doPrint(printer, documents) {
    const task = {
      taskID: getUUID(8, 16),
      preview: false,
      printer,
      documents
    };
    return new Promise((resolve, reject) => {
      this.socket.task({
        cmd: 'print',
        postData: { task },
        callback(data) {
          if (!data) {
            return;
          }
          skynet.info(
            '打印组件返回通知',
            { printer, ...data, documentID: documents[0].documentID },
            { documentID: documents[0].documentID },
          );
          const { cmd, msg, taskStatus } = data;
          // 如果打印报错，则reject
          if (cmd === 'notifyPrintResult' && taskStatus === 'failed') {
            return reject(msg);
          }
          // 否则，打印完成后resolve
          if (cmd === 'notifyPrintResult' && taskStatus === 'rendered') {
            resolve(void 0);
          }
        }
      });
    });
  }
}

export default PrintService;

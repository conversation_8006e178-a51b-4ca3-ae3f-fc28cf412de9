import PrintService from './print-service';

let printerService: any = null;
const getPrinterService = () => {
  if (!printerService || !printerService.socket.isOpen) {
    printerService = new PrintService();
  }
  return printerService;
};

// 获取打印机
export const getPrinters = () => {
  return getPrinterService()
    .getPrinters()
    .then((res) => {
      const { printers = [] } = res;
      return printers
        .map((item) => ({ ...item, id: item.name }))
        .filter((vo) => vo.status === 'enable') as [];
    })
    .catch(() => {
      return [];
    });
};

// 执行打印任务
export const doPrint = (printerName, sendExpressRes) => {
  const { printData: printDataString, printDataListStr, expressNo } = sendExpressRes;
  if (!printDataString && !printDataListStr) {
    return Promise.reject('未获取到打印报文');
  }
  const documentConfig = {
    documentID: expressNo,
    contents: []
  };
  if (printDataListStr) {
    try {
      documentConfig.contents = JSON.parse(printDataListStr);
    } catch (err) {
      return Promise.reject('打印报文格式不正确');
    }
  } else {
    try {
      documentConfig.contents = [JSON.parse(printDataString)];
    } catch (err) {
      return Promise.reject('打印报文格式不正确');
    }
  }

  const documents = [documentConfig];
  return getPrinterService().doPrint(printerName, documents);
};

// 执行多个打印任务
export const doBatchPrint = (printerName, printerList) => {
  const isError = printerList.some(print => !print.printData && !print.printDataListStr);
  if (isError) {
    return Promise.reject('未获取到打印报文');
  }

  const documents = printerList.map(item => {
    const documentConfig = {
      documentID: item.expressNo,
      contents: [],
    };
    try {
      if (item.printDataListStr) {
        documentConfig.contents = JSON.parse(item.printDataListStr);
      } else {
        documentConfig.contents = [JSON.parse(item.printDataString)];
      }
    } catch (err) {}
    return documentConfig;
  });

  console.log('开始批量打印：', documents);

  return getPrinterService().doPrint(printerName, documents);
};

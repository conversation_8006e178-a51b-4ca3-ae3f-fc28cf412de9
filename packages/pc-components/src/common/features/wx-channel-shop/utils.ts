import { IOrderInfo } from '@youzan/order-domain-definitions/es/order';
import { ChannelAccountType } from '@youzan/order-domain-definitions/es/wx-channel-shop';
import { safeJsonParse } from '@youzan/order-domain-utils';
import { WxChannelShopPrinter } from 'wx-channel-shop-printer';
import {
  getMultiPlayWaybillOrderPrintMessage,
  updateMultiPlayWaybillOrderPrintStatus
} from './apis';

export function getMpIdFromOrderInfo(orderInfo: IOrderInfo) {
  const data = safeJsonParse((orderInfo.mainOrderInfo.extra as any)?.bIZ_ORDER_ATTRIBUTE) || {};
  return data.XIAODIAN_PROMOTER_MP_ID;
}

export function printWxChannelShopEWaybill({
  salesKdtId,
  mpId,
  thirdTemplateId,
  thirdWaybillId,
  expressNo,
  expressId,
  printer,
  printerName,
  isRePrinting = false
}: {
  salesKdtId: number;
  mpId: number;
  printer: WxChannelShopPrinter;
  thirdTemplateId: string;
  thirdWaybillId: string;
  expressNo: string;
  expressId: number;
  printerName: string;
  isRePrinting?: boolean;
}) {
  const printNum = {
    sumNum: 1,
    curNum: 1
  };
  const promise = getMultiPlayWaybillOrderPrintMessage({
    salesKdtId,
    thirdTemplateId,
    thirdWaybillId,
    printNum,
    mpId,
    channelType: ChannelAccountType.WxChannelShop
  }).then((data) => {
    return printer.print({
      version: '2.0',
      taskList: [
        {
          printInfo: data.printInfo,
          printNum
        }
      ],
      printer: printerName
    });
  });

  return isRePrinting
    ? promise
    : promise.then(() => {
        return updateMultiPlayWaybillOrderPrintStatus({
          salesKdtId,
          thirdWaybillId,
          expressNo,
          expressId,
          channelType: ChannelAccountType.WxChannelShop,
          mpId,
          printType: isRePrinting ? 1 : 0
        });
      });
}

import retailAjax from '@youzan/retail-ajax';
import {
  IExpressAggregateDTO,
  IExpressQueryRequest,
  IExpressSiteQueryRequest,
  IListWithPaginatorVO,
  IMonthlyCardIdDTO,
  IPageQueryResult,
  IPageRequest,
  IProductTypeDTO,
  IShopAddressInfo,
  IShopAddressPageQueryRequest,
  ISiteAggregateDTO,
  IWaybillDeliveryTemplateGetRequest,
  IWaybillDeliveryTemplateResponse,
  IWaybillOrderCreateRequest,
  IWaybillOrderCreateResponse,
  IWaybillOrderPrintGetRequest,
  IWaybillOrderPrintGetResponse,
  IWaybillOrderPrintNotifyRequest
} from './types';

export const getMultiPlatExpressInfos = (data: IExpressQueryRequest) => {
  return retailAjax<IExpressAggregateDTO[]>({
    url: '/v2/order/api/wx-channel-shop/get-multi-plat-express-info.json',
    method: 'GET',
    data
  });
};

export const getMultiPlatPageSites = (data: IExpressSiteQueryRequest & IPageRequest) => {
  return retailAjax<IListWithPaginatorVO<ISiteAggregateDTO>>({
    url: '/v2/order/api/wx-channel-shop/get-multi-plat-page-sites.json',
    method: 'GET',
    data: {
      ...data,
      options: data.options ? JSON.stringify(data.options) : undefined
    }
  });
};

export const queryExpressProductTypes = (data: IExpressSiteQueryRequest) => {
  return retailAjax<Array<IProductTypeDTO>>({
    url: '/v2/order/api/wx-channel-shop/query-express-product-types.json',
    method: 'GET',
    data
  });
};

export const queryPageMonthlyCards = (data: IExpressSiteQueryRequest & IPageRequest) => {
  return retailAjax<IListWithPaginatorVO<IMonthlyCardIdDTO>>({
    url: '/v2/order/api/wx-channel-shop/query-page-monthly-cards.json',
    method: 'GET',
    data
  });
};

export const getWaybillDeliveryTemplate = (data: IWaybillDeliveryTemplateGetRequest) => {
  return retailAjax<IWaybillDeliveryTemplateResponse>({
    url: '/v2/order/api/wx-channel-shop/get-waybill-delivery-template.json',
    method: 'GET',
    data
  });
};

export const createMultiPlayWaybillOrder = (data: IWaybillOrderCreateRequest) => {
  return retailAjax<IWaybillOrderCreateResponse>({
    url: '/v2/order/api/wx-channel-shop/create-multi-plat-waybill-order.json',
    method: 'POST',
    contentType: 'application/json',
    data
  });
};

export const getMultiPlayWaybillOrderPrintMessage = (data: IWaybillOrderPrintGetRequest) => {
  return retailAjax<IWaybillOrderPrintGetResponse>({
    url: '/v2/order/api/wx-channel-shop/get-multi-plat-waybill-order-print-message.json',
    method: 'GET',
    data: {
      ...data,
      printNum: data.printNum ? JSON.stringify(data.printNum) : undefined
    }
  });
};

export const updateMultiPlayWaybillOrderPrintStatus = (data: IWaybillOrderPrintNotifyRequest) => {
  return retailAjax<boolean>({
    url: '/v2/order/api/wx-channel-shop/update-multi-plat-waybill-order-print-status.json',
    method: 'POST',
    contentType: 'application/json',
    data
  });
};

/**
 * 0： 未定义地址
 * 1： 退货地址
 * 2： 发票地址
 * 4： 发货地址
 */
export enum AddressTypeValue {
  Undefined = 0,
  Return = 1,
  Invoice = 2,
  Delivery = 4
}

export const getDeliveryAddressList = (data: IShopAddressPageQueryRequest) => {
  return retailAjax<IPageQueryResult<IShopAddressInfo>>({
    url: '/v2/order/api/wx-channel-shop/get-delivery-address-list.json',
    method: 'GET',
    data: {
      ...data,
      addressTypeValues: data.addressTypeValues ? JSON.stringify(data.addressTypeValues) : undefined
    }
  });
};

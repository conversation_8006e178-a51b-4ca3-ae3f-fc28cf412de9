import { ISelectItem } from 'zent';

/**
 * `zent@9.x` 的 `Select`, `value` 是 `ISelectItem` 结构, 比较反人类; 把 `value` 适配为 `@zent/compat` `Select` 的 `value` 结构
 */
export function useZentCompatSelectProps<TValue extends string | number>({
  value,
  options,
  onChange
}: {
  value: TValue;
  options: ISelectItem<TValue>[];
  onChange: (value: TValue) => void;
}) {
  const selectedOption = options.find(item => item.key === value) || null;
  const handleChange = (value: ISelectItem<TValue>) => {
    onChange(value.key);
  };
  return {
    value: selectedOption,
    options,
    onChange: handleChange
  };
}

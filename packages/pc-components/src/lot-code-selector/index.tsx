import { LotCodeItem } from '@youzan/order-domain-definitions/es/lot-code';
import keyBy from 'lodash/keyBy';
import * as React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { Grid, Notify, NumberInput } from 'zent';
import { getLotCodeList } from './apis';

export interface LotCodeSelectorValue {
  selectedLotCodes: string[];
  selectedLotCodeNumMap: Record<string, number>;
}

export interface LotCodeSelectorProps {
  className?: string;
  value: LotCodeSelectorValue;
  goodsId: number;
  skuId: number;
  max?: number;
  orderNo?: string;
  orderKdtId?: number;
  orderItemId?: string;
  onChange: (value: LotCodeSelectorValue) => void;
}

const PageSize = 5;

export function LotCodeSelector({
  className,
  value,
  goodsId,
  skuId,
  orderNo,
  orderKdtId,
  orderItemId,
  onChange
}: LotCodeSelectorProps): JSX.Element {
  const [loading, setLoading] = useState(false);
  const [current, setCurrent] = useState(1);
  const [datasets, setDatasets] = useState<LotCodeItem[]>([]);
  const [datasetMap, setDatasetMap] = useState<Record<string, LotCodeItem>>({});

  const columns = [
    {
      title: '批号',
      name: 'lotCode'
    },
    {
      title: '可用库存',
      name: 'num',
      bodyRender(data: LotCodeItem) {
        const { num } = data;
        return `${num}件`;
      }
    },
    {
      title: '发货数量',
      name: 'fulfillNum',
      bodyRender(data: LotCodeItem) {
        const { lotCode } = data;
        return (
          <NumberInput
            width={79}
            value={value.selectedLotCodeNumMap[lotCode]}
            min={0}
            max={datasetMap[lotCode]?.num}
            integer
            placeholder="请输入数量"
            onChange={(numValue) => {
              let { selectedLotCodes } = value;
              if ((numValue || 0) > 0 && value.selectedLotCodes.indexOf(lotCode) === -1) {
                // 自动勾选
                selectedLotCodes = value.selectedLotCodes.concat(lotCode);
              }
              onChange({
                selectedLotCodes,
                selectedLotCodeNumMap: {
                  ...value.selectedLotCodeNumMap,
                  [lotCode]: numValue || 0
                }
              });
            }}
          />
        );
      }
    }
  ];

  const updateLotCodesList = useCallback(() => {
    setLoading(true);
    return getLotCodeList({
      goodsId,
      skuId,
      ...(orderNo && orderItemId && orderKdtId
        ? {
            orderNo,
            orderItemId,
            orderKdtId
          }
        : {})
    })
      .then((data) => {
        setDatasets(data);
        setDatasetMap(keyBy(data, 'lotCode'));
      })
      .catch((err) => {
        Notify.error(err?.msg || '批号获取失败');
        throw err;
      })
      .finally(() => {
        setLoading(false);
      });
  }, [goodsId, skuId, orderNo, orderItemId, orderKdtId]);

  useEffect(() => {
    updateLotCodesList();
  }, [updateLotCodesList]);

  return (
    <Grid
      className={className}
      loading={loading}
      columns={columns}
      pageInfo={{
        pageSize: PageSize,
        total: datasets.length,
        current
      }}
      datasets={datasets.slice((current - 1) * PageSize, current * PageSize)}
      selection={{
        selectedRowKeys: value.selectedLotCodes,
        onSelect: (selectedRowKeys) => {
          onChange({
            ...value,
            selectedLotCodes: selectedRowKeys
          });
        }
      }}
      rowKey="lotCode"
      bordered={false}
      onChange={({ current }) => setCurrent(current || 1)}
    />
  );
}

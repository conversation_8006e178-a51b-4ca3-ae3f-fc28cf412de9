import { LotCodeItem } from '@youzan/order-domain-definitions/es/lot-code';
import { LotCodeSelectorValue } from '.';

// 虽然定义一样, 含义不一样
// LotCodeSelectedItem.num 是选择数量
export type LotCodeSelectedItem = LotCodeItem;

export function getLotCodeSelectedItems(value: LotCodeSelectorValue): LotCodeSelectedItem[] {
  return value.selectedLotCodes.map((lotCode) => ({
    lotCode,
    num: value.selectedLotCodeNumMap[lotCode]
  }));
}

/**
 * 删除未选择或者数量为 0 的批号项
 */
export function formatLotCodeSelectorValue(value: LotCodeSelectorValue): LotCodeSelectorValue {
  const selectedLotCodes = value.selectedLotCodes.filter(
    (lotCode) => value.selectedLotCodeNumMap[lotCode] > 0
  );
  return {
    selectedLotCodes,
    selectedLotCodeNumMap: selectedLotCodes.reduce((map, lotCode) => {
      map[lotCode] = value.selectedLotCodeNumMap[lotCode];
      return map;
    }, {} as Record<string, number>)
  };
}

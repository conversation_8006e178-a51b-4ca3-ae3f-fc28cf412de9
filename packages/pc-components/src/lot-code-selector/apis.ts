import retailAjax from '@youzan/retail-ajax';

import { LotCodeItem } from '@youzan/order-domain-definitions/es/lot-code';

export type GetLotCodeListParams = {
  goodsId: number;
  skuId: number;
  orderNo?: string;
  orderItemId?: string;
  orderKdtId?: number;
};
export function getLotCodeList(data: GetLotCodeListParams): Promise<LotCodeItem[]> {
  return retailAjax({
    method: 'GET',
    url: '/youzan.retail.trade.lot.query.inventory/1.0.1',
    data
  });
}

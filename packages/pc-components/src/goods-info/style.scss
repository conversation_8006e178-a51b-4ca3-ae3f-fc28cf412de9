@import '../../style';

.gray-text {
  color: $color-text-secondary;
  line-height: 18px;
}

.od-express-content {
  .goods-info {
    &__title {
      &--with-brief {
        display: flex;
        justify-content: space-between;

        .goods-info__title-link {
          display: inline-block;
        }

        .goods-brief-wrapper {
          margin-left: 5px;
        }
      }
    }

    &__sku-no,
    &__goods-no {
      word-break: break-word;
    }

    &__img {
      width: 60px;
      height: 60px;
      min-width: 60px;
      min-height: 60px;
      margin-right: 10px;
    }

    &__icon {
      border: 1px solid $color-alert;
      color: $color-alert;
      border-radius: 3px;
      padding: 0 5px;
      margin-right: 5px;
      line-height: 18px;
    }
  }
}


.od-goods-info {
  display: flex;
  padding: 10px 12px 10px 0;

  &__combo_detail_str {
    display: flex;
    margin-top: 2px;
    word-break: break-all;
    font-size: $font-size-small;
    color: $color-text-secondary;
    &__info {
      width: 120px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    span {
      flex-shrink: 0;
      width: 26px;
      color: $color-link;
      cursor: pointer;
    }
  }
}

.od-combo-detail-pop {
  width: 300px;
}

.od-oversold {
  max-width: 210px;
}

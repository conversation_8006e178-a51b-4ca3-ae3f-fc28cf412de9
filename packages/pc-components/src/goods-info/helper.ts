type SkuShape = Record<'k' | 'v', string>;

/**
 * 处理 JSON 字符串格式的 SKU 信息
 * @param {string} skuJSONStr
 */
const processSkuJSONStr = (skuStr: string) =>
  JSON.parse(skuStr)
    .map((sku: SkuShape) => `${sku.k}：${sku.v}`)
    .join(' ');

/**
 * 有些数据, 传递 skuDesc, 有些传递 sku
 * @param {object} param0
 */
export const getSkuStr = ({ sku, skuDesc }: Record<'sku' | 'skuDesc', string>) => {
  if (sku) {
    return processSkuJSONStr(sku);
  }

  return skuDesc;
};

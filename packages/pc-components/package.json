{"name": "@youzan/order-domain-pc-components", "version": "1.0.21-beta.20250529200619.0", "main": "./es/index.js", "module": "./es/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "yarn && yarn build:scss && zan-doc", "build": "tsc && yarn build:scss", "build:scss": "node ./scripts/build-styles.js", "build:watch": "rm -rf ./css && concurrently \"yarn build:scss && tsc -w\" \"npx nodemon -x yalc push\" ", "yalc:watch": "rm -rf ./css && yarn build:scss && tsc -w", "yalc:link": "npx nodemon -x yalc push", "prepublishOnly": "yarn build"}, "author": "RETAIL FE TEAM", "license": "ISC", "description": "", "files": ["es", "css"], "peerDependencies": {"@youzan/retail-components": "4.10.0", "@zent/compat": "2.0.0", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "5.1.0", "zent": "^9.7.2"}, "sideEffects": ["*.css"], "devDependencies": {"@types/lodash": "^4.14.123", "@types/react": "^17.0.11", "@types/react-dom": "^17.0.8", "@types/react-router-dom": "^4.3.3", "@youzan/retail-components": "4.10.0", "@zent/compat": "2.0.0", "autoprefixer": "^10.2.6", "fs-extra": "^10.0.0", "globby": "^11.0.4", "postcss": "^8.3.5", "postcss-import": "^14.0.2", "react": "^17.0.2", "react-dom": "^17.0.2", "sass": "^1.35.1", "zent": "9.7.2"}, "dependencies": {"@types/classnames": "^2.2.7", "@youzan/order-domain-definitions": "1.0.1", "@youzan/order-domain-utils": "1.0.1-beta.20240828172048.0", "@youzan/react-components": "4.10.0", "@youzan/react-hooks": "1.1.1", "@youzan/retail-ajax": "^1.3.1", "@youzan/retail-armor": "2.1.1", "@youzan/retail-utils": "5.10.5", "@youzan/sam-components": "1.1.0", "date-fns": "^1.30.1", "lodash": "^4.17.0", "prop-types": "^15.6.0", "react-router-dom": "5.1.0", "wx-channel-shop-printer": "0.1.0"}}
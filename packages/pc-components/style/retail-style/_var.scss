@import './_base-color.scss';

/**
 * 推荐安装 [SCSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=mrmlnc.vscode-scss)
 * !!! IMPORTANT: 在 retail-style 中的所有 .scss 文件, 注释请使用「/*」, 不能用「//」
 * !!! sm2(目前最新 2.1.11)会有问题--注释下一行不会解析, 导致变量原样输出, 无法引用变量
 * !!! 貌似是 postcss 插件问题, sass 没有影响.
 * !!! 为了兼容 .pcss 和 .scss 文件, 只能用「/*」 注释了
 */

/**
 * 字体 {{{1

 */

$font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, Roboto, Arial,
  PingFang SC, Hiragino Sans GB, Microsoft Yahei, SimSun, sans-serif !default;

/* 字体大小 */
$font-size-base: 14px !default;
$font-size-small: 12px !default;
$font-size-medium: 16px !default;
$font-size-large: 20px !default;
$font-size-xlarge: 26px !default;
$font-size-xxlarge: 34px !default;
$font-size-xxxlarge: 44px !default;

/* line-height */
$font-line-height-base: 20px !default;
$font-line-height-small: 18px !default;
$font-line-height-medium: 24px !default;
$font-line-height-large: 28px !default;
$font-line-height-xlarge: 36px !default;
$font-line-height-xxlarge: 44px !default;
$font-line-height-xxxlarge: 52px !default;

/* font-weight */
$font-weight-regular: 400 !default;
$font-weight-medium: 500 !default;
$font-weight-semibold: 600 !default;

/**
 * 颜色 {{{1
 */

$color-white: $color-w !default;
$color-black: $color-b !default;

/* 文字颜色 */
$color-text-primary: $color-n8 !default;
$color-text-light-primary: $color-n7 !default;
$color-text-secondary: $color-n6 !default;
$color-text-disable: $color-n5 !default;

/* 背景色 */
$background-color-base: $color-n1 !default;
$background-color-dark-base: $color-n2 !default;

/* 边框 */
$border-color-base: $color-n3 !default;
$border-color-dark-base: $color-n4 !default;

/**
 * 颜色.功能色 {{{2
 */

$color-success: $color-g4 !default;
$border-color-success: #66be74 !default;
$background-color-success: #f0faf2 !default;

$color-alert: $color-r4 !default;
$border-color-alert: #df4545 !default;
$background-color-alert: #ffebeb !default;

$color-warn: #ed6a0c !default;
$border-color-warn: #f1924e !default;
$background-color-warn: #fff5ed !default;

/**
 * 颜色.衍生色 {{{2
 */

/* 链接 */
$color-link: $color-b4 !default;
$color-link-hover: $color-b4-1 !default;
$color-link-active: $color-b4-2 !default;
$color-link-disable: $color-text-disable !default;

/* 按钮 */
$color-primary-button-hover: $color-b4-1 !default;
$color-primary-button-active: $color-b4-2 !default;

$color-alert-button-hover: $color-r4-1 !default;
$color-alert-button-active: $color-r4-2 !default;

$color-success-button-hover: $color-g4-1 !default;
$color-success-button-active: $color-g4-2 !default;

$color-secondary-button-hover: $color-n8-1 !default;
$color-secondary-button-active: $color-n8-2 !default;

/* 打分 */
$color-rate: #fad20c !default;

/**
 * 颜色.业务 {{{2
 */

$color-lynch: #74829d;
$color-indigo: #4c54bc;
$color-electric-violet: #5f44ff;
$color-thunderbird: #c53d1c;
$color-lochmara: #0177d4;
$color-jungle-green: #22a266;
$color-shark: #252728;
$color-cornflower-blue: #5f96fd;
$color-pale-sky: #626b86;
$color-roti: #cfa04a;

/**
 * 暂时性填坑, 坑填完后直接删掉
 */
$color-light-gray: #bbb !default;
$color-gray: #999 !default;
$color-dark-gray: #666 !default;
$color-orange: #f60 !default;
$color-green: #4b0 !default;
$color-red: #f30 !default;

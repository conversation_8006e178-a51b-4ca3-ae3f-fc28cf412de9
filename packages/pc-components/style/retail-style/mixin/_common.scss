@mixin size($width, $height) {
  $height: $width !default;

  width: $width;
  height: $height;
}

@mixin circle($size) {
  @include size($size);

  border-radius: 50%;
}

@mixin clearfix {
  &::after {
    clear: both;
    content: '';
    display: block;
  }
}

@mixin pseudo($content: '', $display: block) {
  content: $content;
  display: $display;
}

@mixin triangle($direction: right, $size: 6px, $color: #ddd) {
  width: 0;
  height: 0;

  @include pseudo();

  @if $direction == down {
    border-left: $size solid transparent;
    border-right: $size solid transparent;
    border-top: $size solid $color;
  } @else if $direction == up {
    border-left: $size solid transparent;
    border-right: $size solid transparent;
    border-bottom: $size solid $color;
  } @else if $direction == right {
    border-top: $size solid transparent;
    border-bottom: $size solid transparent;
    border-left: $size solid $color;
  } @else {
    border-top: $size solid transparent;
    border-bottom: $size solid transparent;
    border-right: $size solid $color;
  }
}

@mixin ellipsis($width: 100%, $display: inline-block, $line: 1) {
  display: $display;
  max-width: $width;
  text-overflow: ellipsis;
  overflow: hidden;

  @if $line > 1 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
  } @else {
    white-space: nowrap;
  }
}

@mixin flex($direction: column, $alignItems: false, $justifyContent: false) {
  display: flex;

  @if $alignItems {
    align-items: $alignItems;
  }

  @if $justifyContent {
    justify-content: $justifyContent;
  }

  @if $direction {
    flex-direction: $direction;
  }
}

@mixin border1px($direction: bottom, $borderColor: #ddd) {
  position: relative;

  ::after {
    @include pseudo($pos: absolute);

    @if $direction == top {
      left: 0;
      width: 200%;
      height: 1px;
    } @else if $direction == right {
      right: 0;
      width: 1px;
      height: 200%;
    } @else if $direction == bottom {
      bottom: 0;
      left: 0;
      width: 200%;
      height: 1px;
    } @else {
      left: 0;
      width: 1px;
      height: 200%;
    }

    background-color: $borderColor;
    transform: scale(0.5);
    transform-origin: 0 0;
  }
}

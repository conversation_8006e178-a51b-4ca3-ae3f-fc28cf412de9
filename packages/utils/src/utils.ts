import { checkLiteOnlineStoreManager } from '@youzan/utils-shop';
import { get, isEqual } from 'lodash';

export const IsLiteOnlineStoreManager = checkLiteOnlineStoreManager(
  get(window, '_global.business.userInfo.roles')
);

export function safeJsonParse(data: string | undefined) {
  if (typeof data !== 'string') return data;
  try {
    return JSON.parse(data);
  } catch (e) {
    return undefined;
  }
}

/**
 * 将数组里key值相同的项通过fn进行合并
 */
export function compressBy<T extends Record<string, any>>(
  objArray: T[],
  key: string,
  fn = (a: T, b?: T): T => ({ ...a, ...b })
): T[] {
  return objArray.reduce((pre: T[], current: T) => {
    let isExisted = false;
    const currentArray = pre.map((item) => {
      if (isEqual(item[key], current[key])) {
        isExisted = true;
        return fn(item, current);
      }
      return item;
    }, []);

    if (!isExisted) {
      return currentArray.concat(fn(current));
    }
    return currentArray;
  }, []);
}

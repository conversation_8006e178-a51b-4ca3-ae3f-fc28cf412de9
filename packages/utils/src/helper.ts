import { times } from '@youzan/retail-utils';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { ExpressType } from '@youzan/zan-hasaki';
import { get } from 'lodash';
import * as queryString from 'query-string';
import { DeliveryChannelType, IOrderInfo } from '@youzan/order-domain-definitions/es/order';

const CALC_NUMBER = 1000;

/**
 * 从 window.location.hash 中获取 query string
 *
 * 原因：项目中，很多地方使用了 hash route，导致 query string 放在了 hash 之后，
 * 于是
 * > window.location.hash => '#/?a=xxx'
 * >
 * > window.location.search => ''
 *
 * 所以无法正确使用 queryString，于是用此函数去除多余的部分，便于 parse
 *
 * ⚠️ **会将 snake-case 转换为 camelCase** ⚠️
 */
export function getQueryFromHash(): Record<string, unknown>;
export function getQueryFromHash(type: 'string'): string;
export function getQueryFromHash(typeOrEmpty?: 'string'): any {
  const RemoveBeforeQueryString = /#.*?[^\?]/; // 用于 移除 query-string 之前多余的 字符
  const pureQuery = window.location.hash.replace(RemoveBeforeQueryString, '');

  if (typeOrEmpty === 'string') {
    return pureQuery;
  }

  return mapKeysToCamelCase(queryString.parse(pureQuery) ?? {}, true);
}

/**
 * 检查 query string，判断当前页面是否应该直接打开「发货弹窗」
 *
 * 当 URL 中 open_fulfill_dialog 为 truthy 时，返回 true，否则返回 false
 */
export const shouldOpenFulfillDialog = (): boolean => {
  const queryMap = getQueryFromHash();
  const { openFulfillDialog } = queryMap;
  return !!openFulfillDialog;
};

// 商品数量乘于CALC_NUMBER
export const timesGoodsNum = (num: number): number => times(num, CALC_NUMBER);

export function isGYYOrder(orderInfo: IOrderInfo): boolean {
  return (
    orderInfo.mainOrderInfo?.channelType === DeliveryChannelType.GYY ||
    orderInfo.refundOrderInfo?.channelType === DeliveryChannelType.GYY
  );
}

/**
 * 是否为美团外卖订单
 *
 * @param orderInfo
 * @returns
 */
export function checkIsMeituanOrder(channelType?: number): boolean {
  return channelType === DeliveryChannelType.Meituan;
}

/**
 * 是否为美团闪购订单
 *
 * @param orderInfo
 * @returns
 */
export function checkIsMeituanShangouOrder(channelType?: number): boolean {
  return channelType === DeliveryChannelType.MeituanShangou;
}

/**
 * 是否为美团平台订单
 */
export function checkIsMeituanPlatformOrder(channelType?: number): boolean {
  return checkIsMeituanOrder(channelType) || checkIsMeituanShangouOrder(channelType);
}

/**
 * 判断是否为「第三方订单」
 */
export function isOuterOrder(
  orderInfo: Pick<IOrderInfo, 'mainOrderInfo' | 'refundOrderInfo'>
): boolean {
  return (
    +get(orderInfo, 'mainOrderInfo.bizType')! === 7 ||
    +get(orderInfo, 'refundOrderInfo.bizType')! === 7
  );
}

/**
 * 判断是否为需要 快递发货 的三方订单(例如管易云)
 */
export const isThirdPartyDelivery = (
  orderInfo: Pick<IOrderInfo, 'mainOrderInfo' | 'refundOrderInfo'>
): boolean => {
  return isOuterOrder(orderInfo) && orderInfo.mainOrderInfo?.expressType === ExpressType.Express;
};

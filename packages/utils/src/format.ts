import { div, isValidNumber, keepDecimal, numberWithCommas } from '@youzan/retail-utils';
import { isNil } from 'lodash';

export function formatMoney(value: number): string {
  if (isNil(value)) {
    return value;
  }
  return numberWithCommas((+value).toFixed(2));
}

/**
 * 将金额「分」，处理为「元」，精度为小数点后两位，例如
 *
 * 0 => 0.00
 *
 * 4000 => 40.00
 *
 * 亦支持负数
 */
export function convertFenToYen(centPrice: number): string {
  if (isNil(centPrice)) {
    return centPrice;
  }
  return formatMoney(div(+centPrice || 0, 100));
}

/**
 * 渲染价格的字符串，处理正负数
 *
 * @param centPrice
 * @returns
 */
export function renderPriceString(centPrice: number): string {
  // 正数，直接渲染
  if (centPrice >= 0) return `￥${convertFenToYen(centPrice)}`;
  return `-￥${Math.abs(+convertFenToYen(centPrice)).toFixed(2)}`;
}

// 后端金额‘分’转'元'，用于前端计算和展示
export const transformYuan = (val: number): number => +keepDecimal(div(val, 100), 2);

// 利用Math.round进行金额格式化
export function formatMoneyRound(price: number): string | number {
  if (!isValidNumber(price)) {
    return price;
  }
  const val = div(Math.round(price), 100);
  return numberWithCommas(val);
}

export const transformFenToYuan = (amount: number) => {
  return div(amount, 100);
};

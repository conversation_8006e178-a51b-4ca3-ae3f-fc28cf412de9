{
  "compilerOptions": {
    "allowJs": true,
    "sourceMap": true,
    "declarationMap": true,
    "noImplicitThis": false,
    "resolveJsonModule": true,
    "jsx": "react",
    "noImplicitAny": false,
    "experimentalDecorators": true,
    "target": "ES5",
    "module": "ESNext",
    "lib": ["ES2019", "DOM"],
    "declaration": true,
    "outDir": "./es",
    "noEmitOnError": true,
    "moduleResolution": "node",
    "strict": false,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": "./src",
    "paths": {
      "@youzan/order-domain-utils/*": ["../../packages/utils/src/*"],
    }
  },
  "include": ["./index.d.ts", "./src/**/*"],
  "exclude": ["es/**/*"]
}
